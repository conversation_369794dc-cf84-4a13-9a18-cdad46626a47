# HohoAI 模型配置文件
# 程序会自动读取此配置

# ==================== 模型设置 ====================
# 默认YOLO模型文件名（程序会自动搜索）
default_yolo_model=yolov8n.pt

# 模型搜索路径（按优先级排序）
model_search_paths=models/yolo,models/weights,models,.

# 模型置信度阈值
model_confidence=0.6

# 是否启用GPU加速
use_gpu=true

# ==================== 错误处理 ====================
# 是否显示详细错误信息
verbose_errors=true

# 是否在控制台打印模型加载信息
print_model_info=true

# 模型加载失败时的备用方案
fallback_to_cpu=true

# ==================== 性能设置 ====================
# 模型推理设备 (auto/cpu/cuda/mps)
inference_device=auto

# 批处理大小
batch_size=1

# 图像输入尺寸
input_size=640
