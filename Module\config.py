import os
from pathlib import Path
import sys
from typing import Any
import json

Root = Path(os.path.realpath(sys.argv[0])).parent


class Config:
    default = {
        "log_level": "info",
        "aim_range": 150,
        "aimBot": True,
        "confidence": 0.3,
        "aim_speed_x": 6.7,
        "aim_speed_y": 8.3,
        "model_file": "yolov8n.pt",
        "mouse_Side_Button_Witch": True,
        "ProcessMode": "single_process",
        "window_always_on_top": False,
        "target_class": "0",
        "lockKey": "VK_RBUTTON",
        "triggerType": "按下",
        "offset_centery": 0.75,
        "offset_centerx": 0.0,
        "screen_pixels_for_360_degrees": 6550,
        "screen_height_pixels": 3220,
        "near_speed_multiplier": 2.5,
        "slow_zone_radius": 0,
        "mouseMoveMode": "win32",
        "lockSpeed": 5.5,
        "jump_suppression_switch": False,
        "jump_suppression_fluctuation_range": 18,
        "jump_detection_switch": False,  # WASD检测开关，用于自动扳机冷却功能，默认关闭
        "automatic_trigger_switch": False,  # 自动扳机开关
        # KmBoxNet 默認連線參數
        "KmBox_IP": "*************",
        "KmBox_PORT": "35936",
        "KmBox_MAC": "ACE03CAB",
        # 自动机器人默认设置
        "auto_robot_enabled": False,
        "auto_robot_hotkey_vk": 71,  # G键
        "auto_robot_hotkey_hex": "47",  # G键的十六进制码
        # 动态瞄准范围设置
        "dynamic_aim_range_enabled": True,  # 是否启用动态瞄准范围
        "target_aim_range_scale": 0.6  # 有目标时瞄准范围缩放比例（0.6表示缩小到60%）
    }
    content = None

    @classmethod
    def read(cls) -> dict:
        try:
            os.makedirs(Root / "Data", exist_ok=True)

            # 优先读取带注释的配置文件
            commented_config_path = Root / "settings_with_comments.json"
            if commented_config_path.exists():
                with open(commented_config_path, "r", encoding="utf-8") as f:
                    content = f.read()
                    # 移除注释
                    lines = content.split('\n')
                    cleaned_lines = []
                    for line in lines:
                        comment_pos = line.find('//')
                        if comment_pos != -1:
                            before_comment = line[:comment_pos]
                            quote_count = before_comment.count('"') - before_comment.count('\\"')
                            if quote_count % 2 == 0:
                                line = line[:comment_pos].rstrip()
                        if line.strip():
                            cleaned_lines.append(line)
                    clean_content = '\n'.join(cleaned_lines)
                    return json.loads(clean_content)

            # 如果没有带注释的配置文件，读取普通配置文件
            with open(Root / "Data" / "settings.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except FileNotFoundError:
            return cls.default

    @classmethod
    def get(cls, key: str, default: Any = None) -> Any:
        """
        获取配置项的值，如果不存在则返回默认值。

        :param key: 配置项的键
        :param default: 默认值
        :return: 返回配置项的值，类型可能是 int, str, list, float 或 bool
        """
        if cls.content is None:
            cls.content = cls.read()  # 读取配置文件
        if default is not None:
            return cls.content.get(key, default)
        return cls.content.get(key, cls.default.get(key))

    @classmethod
    def update(cls, key: str, value: Any) -> None:
        if cls.content is None:
            cls.content = cls.read()
        cls.content[key] = value
        cls.save()

    @classmethod
    def delete(cls, key: str) -> None:
        if cls.content is None:
            cls.content = cls.read()
        if key in cls.content:
            del cls.content[key]
            cls.save()

    @classmethod
    def save(cls) -> None:
        if cls.content is None:
            cls.content = cls.read()

        # 保存到普通配置文件
        with open(Root / "Data" / "settings.json", "w", encoding="utf8") as f:
            f.write(json.dumps(cls.content, ensure_ascii=False, indent=4))

        # 同时更新带注释的配置文件（如果存在）
        commented_config_path = Root / "settings_with_comments.json"
        if commented_config_path.exists():
            try:
                # 读取现有的带注释配置文件
                with open(commented_config_path, "r", encoding="utf-8") as f:
                    content = f.read()

                # 更新配置值但保留注释
                lines = content.split('\n')
                updated_lines = []

                for line in lines:
                    updated_line = line
                    # 查找配置项
                    for key, value in cls.content.items():
                        pattern = f'"{key}":\\s*[^,/]*'
                        if f'"{key}":' in line:
                            # 保留注释部分
                            comment_pos = line.find('//')
                            comment_part = line[comment_pos:] if comment_pos != -1 else ""

                            # 构建新的配置行
                            if isinstance(value, str):
                                new_value = f'"{value}"'
                            elif value is None:
                                new_value = "null"
                            elif isinstance(value, bool):
                                new_value = "true" if value else "false"
                            else:
                                new_value = str(value)

                            # 保持原有的缩进
                            indent = len(line) - len(line.lstrip())
                            spaces = " " * indent

                            if comment_part:
                                key_value_part = f'"{key}": {new_value},'
                                padding = " " * max(1, 50 - len(key_value_part))
                                updated_line = f'{spaces}{key_value_part}{padding}{comment_part}'
                            else:
                                updated_line = f'{spaces}"{key}": {new_value},'
                            break

                    updated_lines.append(updated_line)

                # 写回文件
                with open(commented_config_path, "w", encoding="utf-8") as f:
                    f.write('\n'.join(updated_lines))

            except Exception as e:
                # 如果更新失败，至少保证普通配置文件正常
                pass
