#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
热重载配置系统
监控配置文件变化并实时更新程序配置
"""

import os
import json
import time
import threading
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class ConfigReloadHandler(FileSystemEventHandler):
    """配置文件变化处理器"""
    
    def __init__(self, config_file, callback=None):
        self.config_file = config_file
        self.callback = callback
        self.last_modified = 0
        
    def on_modified(self, event):
        """文件修改时触发"""
        if event.is_directory:
            return
            
        if event.src_path.endswith(self.config_file):
            # 防止重复触发
            current_time = time.time()
            if current_time - self.last_modified < 1:
                return
            self.last_modified = current_time
            
            print(f"🔄 检测到配置文件变化: {event.src_path}")
            self.reload_config()
    
    def reload_config(self):
        """重新加载配置"""
        try:
            # 等待文件写入完成
            time.sleep(0.5)
            
            # 读取配置文件
            with open(self.config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 移除注释
            lines = content.split('\n')
            cleaned_lines = []
            for line in lines:
                comment_pos = line.find('//')
                if comment_pos != -1:
                    before_comment = line[:comment_pos]
                    quote_count = before_comment.count('"') - before_comment.count('\\"')
                    if quote_count % 2 == 0:
                        line = line[:comment_pos].rstrip()
                if line.strip():
                    cleaned_lines.append(line)
            
            clean_content = '\n'.join(cleaned_lines)
            config_data = json.loads(clean_content)
            
            # 更新配置到程序
            self.update_program_config(config_data)
            
            print("✅ 配置已热重载")
            
        except Exception as e:
            print(f"❌ 配置重载失败: {e}")
    
    def update_program_config(self, config_data):
        """更新程序配置"""
        try:
            # 更新Module/config.py中的配置
            import sys
            sys.path.append('.')
            from Module.config import Config
            
            # 清除缓存并更新配置
            Config.content = config_data
            Config.save()
            
            # 显示重要配置变化
            print("📊 配置已更新:")
            print(f"  自动瞄准: {config_data.get('aimBot', 'unknown')}")
            print(f"  辅助压枪: {config_data.get('recoil_suppression_switch', 'unknown')}")
            print(f"  压枪强度: {config_data.get('recoil_strength', 'unknown')}")
            print(f"  检测置信度: {config_data.get('confidence', 'unknown')}")
            
            # 如果有回调函数，调用它
            if self.callback:
                self.callback(config_data)
                
        except Exception as e:
            print(f"❌ 更新程序配置失败: {e}")

class HotReloadManager:
    """热重载管理器"""
    
    def __init__(self, config_file="settings_with_comments.json"):
        self.config_file = config_file
        self.observer = None
        self.handler = None
        
    def start_monitoring(self, callback=None):
        """开始监控配置文件"""
        if not os.path.exists(self.config_file):
            print(f"❌ 配置文件不存在: {self.config_file}")
            return False
        
        try:
            self.handler = ConfigReloadHandler(self.config_file, callback)
            self.observer = Observer()
            
            # 监控当前目录
            self.observer.schedule(self.handler, path='.', recursive=False)
            self.observer.start()
            
            print(f"🔄 开始监控配置文件: {self.config_file}")
            print("💡 现在您可以修改配置文件，保存后立即生效")
            
            return True
            
        except Exception as e:
            print(f"❌ 启动监控失败: {e}")
            return False
    
    def stop_monitoring(self):
        """停止监控"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            print("🛑 配置监控已停止")

def start_hot_reload_daemon():
    """启动热重载守护进程"""
    manager = HotReloadManager()
    
    def config_change_callback(config_data):
        """配置变化回调"""
        # 这里可以添加额外的处理逻辑
        pass
    
    if manager.start_monitoring(config_change_callback):
        try:
            # 保持运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 收到中断信号")
        finally:
            manager.stop_monitoring()
    
if __name__ == "__main__":
    print("🔄 HohoAI 热重载配置系统")
    print("=" * 40)
    start_hot_reload_daemon()
