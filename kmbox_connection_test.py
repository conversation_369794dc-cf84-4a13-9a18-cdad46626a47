#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KmBox连接测试工具
"""

import sys
import os
import json
import time

def test_kmbox_connection():
    """测试KmBox连接"""
    print("🔧 KmBox连接测试工具")
    print("=" * 50)
    
    # 读取配置
    try:
        with open('settings_with_comments.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        ip = config.get('KmBox_IP', '*************')
        port = config.get('KmBox_PORT', '18243')
        mac = config.get('KmBox_MAC', '6FE3E466')
        mouse_mode = config.get('mouseMoveMode', 'win32')
        
        print("📊 当前配置:")
        print(f"  IP地址: {ip}")
        print(f"  端口: {port}")
        print(f"  MAC地址: {mac}")
        print(f"  鼠标模式: {mouse_mode}")
        print()
        
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")
        return False
    
    # 检查kmNet模块
    try:
        sys.path.append('DLLs/python_pyd')
        import kmNet
        print("✅ kmNet模块加载成功")
    except Exception as e:
        print(f"❌ kmNet模块加载失败: {e}")
        print("请检查 DLLs/python_pyd/kmNet.cp310-win_amd64.pyd 文件是否存在")
        return False
    
    # 测试网络连接
    print("\n🌐 测试网络连接...")
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_TCP)
        sock.settimeout(3)
        result = sock.connect_ex((ip, int(port)))
        sock.close()
        
        if result == 0:
            print(f"✅ 网络连接成功: {ip}:{port}")
        else:
            print(f"❌ 网络连接失败: {ip}:{port}")
            print("请检查:")
            print("  1. KmBox设备是否开机")
            print("  2. 网络连接是否正常")
            print("  3. IP地址和端口是否正确")
            return False
    except Exception as e:
        print(f"❌ 网络测试失败: {e}")
        return False
    
    # 测试KmBox初始化
    print("\n🔌 测试KmBox初始化...")
    try:
        # 尝试初始化
        result = kmNet.init(ip, port, mac)
        print(f"📡 kmNet.init() 返回值: {result}")
        
        if result == 0:
            print("✅ KmBox初始化成功")
            
            # 测试基本功能
            print("\n🧪 测试基本功能...")
            
            # 测试移动
            try:
                kmNet.enc_move(1, 1)
                print("✅ 鼠标移动测试成功")
            except Exception as e:
                print(f"❌ 鼠标移动测试失败: {e}")
            
            # 测试监控
            try:
                kmNet.monitor(8888)
                print("✅ 物理键鼠监控启动成功")
            except Exception as e:
                print(f"❌ 物理键鼠监控启动失败: {e}")
            
            return True
        else:
            print(f"❌ KmBox初始化失败，错误码: {result}")
            print("常见错误码:")
            print("  -1: 连接失败")
            print("  -2: MAC地址错误")
            print("  -3: 设备忙碌")
            return False
            
    except Exception as e:
        print(f"❌ KmBox初始化异常: {e}")
        return False

def fix_kmbox_config():
    """修复KmBox配置"""
    print("\n🔧 修复KmBox配置...")
    
    try:
        with open('settings_with_comments.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 确保使用kmbox模式
        config['mouseMoveMode'] = 'kmbox'
        
        # 可选：更新连接参数
        print("当前连接参数:")
        print(f"  IP: {config.get('KmBox_IP', '*************')}")
        print(f"  PORT: {config.get('KmBox_PORT', '18243')}")
        print(f"  MAC: {config.get('KmBox_MAC', '6FE3E466')}")
        
        choice = input("\n是否要修改连接参数? (y/n): ").strip().lower()
        if choice == 'y':
            new_ip = input(f"输入IP地址 [{config.get('KmBox_IP', '*************')}]: ").strip()
            if new_ip:
                config['KmBox_IP'] = new_ip
            
            new_port = input(f"输入端口 [{config.get('KmBox_PORT', '18243')}]: ").strip()
            if new_port:
                config['KmBox_PORT'] = new_port
            
            new_mac = input(f"输入MAC地址 [{config.get('KmBox_MAC', '6FE3E466')}]: ").strip()
            if new_mac:
                config['KmBox_MAC'] = new_mac
        
        # 保存配置
        with open('settings_with_comments.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("✅ 配置已保存")
        print("💡 请重启程序以应用新配置")
        return True
        
    except Exception as e:
        print(f"❌ 修复配置失败: {e}")
        return False

def show_kmbox_status():
    """显示KmBox状态信息"""
    print("\n📊 KmBox状态信息")
    print("=" * 30)
    
    try:
        sys.path.append('DLLs/python_pyd')
        import kmNet
        
        # 这里可以添加更多状态检查
        print("✅ kmNet模块可用")
        
    except Exception as e:
        print(f"❌ kmNet模块不可用: {e}")

def main():
    """主函数"""
    while True:
        print("\n🛠️ KmBox连接测试工具")
        print("=" * 40)
        print("1. 测试KmBox连接")
        print("2. 修复KmBox配置")
        print("3. 显示状态信息")
        print("4. 连接诊断")
        print("0. 退出")
        print("=" * 40)
        
        try:
            choice = input("请选择操作 (0-4): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                success = test_kmbox_connection()
                if success:
                    print("\n🎉 KmBox连接测试通过！")
                    print("💡 现在可以在配置中使用 'mouseMoveMode': 'kmbox'")
                else:
                    print("\n❌ KmBox连接测试失败")
                    print("💡 建议使用选项2修复配置或检查硬件连接")
            elif choice == "2":
                fix_kmbox_config()
            elif choice == "3":
                show_kmbox_status()
            elif choice == "4":
                print("\n🔍 连接诊断")
                print("请检查以下项目:")
                print("1. ✅ KmBox设备是否正确连接电脑")
                print("2. ✅ KmBox设备是否已开机（指示灯亮起）")
                print("3. ✅ 网络连接是否正常")
                print("4. ✅ IP地址是否正确（通常是*************）")
                print("5. ✅ 端口是否正确（通常是18243）")
                print("6. ✅ MAC地址是否正确")
                print("7. ✅ 防火墙是否阻止连接")
                print("8. ✅ 是否有其他程序占用KmBox")
            else:
                print("❌ 无效选择，请重试")
            
            input("\n按回车键继续...")
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")

if __name__ == "__main__":
    main()
