#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HohoAI 模型管理器
自动创建模型文件夹并检测模型文件
"""

import os
import sys
import time

def create_model_structure():
    """创建模型文件夹结构"""
    folders = [
        "models",
        "models/yolo",
        "models/weights", 
        "models/config",
        "models/backup"
    ]
    
    print("📁 创建模型文件夹结构...")
    
    for folder in folders:
        try:
            if not os.path.exists(folder):
                os.makedirs(folder)
                print(f"✅ 创建文件夹: {folder}")
            else:
                print(f"📂 文件夹已存在: {folder}")
        except Exception as e:
            print(f"❌ 创建文件夹失败 {folder}: {e}")
    
    return True

def create_model_readme():
    """创建模型说明文件"""
    readme_content = """# HohoAI 模型文件夹说明

## 📁 文件夹结构

### models/yolo/
放置 YOLO 模型文件：
- yolov8n.pt (推荐)
- yolov8s.pt
- yolov8m.pt
- yolov8l.pt
- yolov8x.pt
- 或其他自定义 YOLO 模型

### models/weights/
放置权重文件：
- 预训练权重
- 自定义训练权重
- 备份权重文件

### models/config/
放置配置文件：
- 模型配置文件
- 类别标签文件
- 训练配置文件

### models/backup/
备份文件夹：
- 旧版本模型
- 备份权重
- 历史配置

## 🎯 推荐模型

1. **yolov8n.pt** - 轻量级，速度快
2. **yolov8s.pt** - 平衡性能和速度
3. **yolov8m.pt** - 中等精度
4. **yolov8l.pt** - 高精度
5. **yolov8x.pt** - 最高精度

## 📥 下载地址

可以从以下地址下载模型：
- https://github.com/ultralytics/ultralytics
- https://github.com/ultralytics/assets/releases

## 🔧 使用说明

1. 将模型文件放入对应文件夹
2. 运行 model_manager.py 检测模型
3. 程序会自动识别并使用模型
"""
    
    try:
        with open("models/README.md", "w", encoding="utf-8") as f:
            f.write(readme_content)
        print("✅ 创建模型说明文件: models/README.md")
    except Exception as e:
        print(f"❌ 创建说明文件失败: {e}")

def scan_models():
    """扫描模型文件"""
    print("\n🔍 扫描模型文件...")
    
    model_extensions = ['.pt', '.onnx', '.engine', '.pb', '.tflite']
    found_models = []
    
    # 扫描所有模型文件夹
    scan_folders = [
        "models/yolo",
        "models/weights",
        "models",
        "."  # 当前目录
    ]
    
    for folder in scan_folders:
        if os.path.exists(folder):
            print(f"\n📂 扫描文件夹: {folder}")
            try:
                files = os.listdir(folder)
                model_files = [f for f in files if any(f.lower().endswith(ext) for ext in model_extensions)]
                
                if model_files:
                    for model_file in model_files:
                        file_path = os.path.join(folder, model_file)
                        file_size = os.path.getsize(file_path)
                        size_mb = file_size / (1024 * 1024)
                        
                        print(f"  ✅ 找到模型: {model_file} ({size_mb:.1f} MB)")
                        found_models.append({
                            'name': model_file,
                            'path': file_path,
                            'size': size_mb,
                            'folder': folder
                        })
                else:
                    print(f"  📭 无模型文件")
                    
            except Exception as e:
                print(f"  ❌ 扫描失败: {e}")
    
    return found_models

def check_model_validity(model_path):
    """检查模型文件有效性"""
    try:
        # 检查文件是否存在
        if not os.path.exists(model_path):
            return False, "文件不存在"
        
        # 检查文件大小
        file_size = os.path.getsize(model_path)
        if file_size < 1024:  # 小于1KB可能是无效文件
            return False, f"文件太小 ({file_size} bytes)"
        
        # 检查文件扩展名
        valid_extensions = ['.pt', '.onnx', '.engine', '.pb', '.tflite']
        if not any(model_path.lower().endswith(ext) for ext in valid_extensions):
            return False, "不支持的文件格式"
        
        # 尝试读取文件头部
        try:
            with open(model_path, 'rb') as f:
                header = f.read(16)
                if len(header) < 16:
                    return False, "文件头部不完整"
        except Exception as e:
            return False, f"无法读取文件: {e}"
        
        return True, "模型文件有效"
        
    except Exception as e:
        return False, f"检查失败: {e}"

def test_model_loading():
    """测试模型加载"""
    print("\n🧪 测试模型加载...")
    
    found_models = scan_models()
    
    if not found_models:
        print("❌ 未找到任何模型文件")
        print("\n📥 请将模型文件放入以下文件夹：")
        print("  - models/yolo/")
        print("  - models/weights/")
        print("  - 当前目录")
        return False
    
    print(f"\n📊 找到 {len(found_models)} 个模型文件")
    
    # 测试每个模型
    valid_models = []
    for model in found_models:
        print(f"\n🔍 检查模型: {model['name']}")
        
        is_valid, message = check_model_validity(model['path'])
        
        if is_valid:
            print(f"  ✅ {message}")
            valid_models.append(model)
            
            # 尝试加载模型（如果是YOLO模型）
            if model['name'].lower().endswith('.pt'):
                try:
                    print(f"  🧪 尝试加载 YOLO 模型...")
                    
                    # 这里可以添加实际的模型加载测试
                    # import torch
                    # model_obj = torch.load(model['path'])
                    
                    print(f"  ✅ 模型加载测试通过")
                    
                except Exception as e:
                    print(f"  ⚠️ 模型加载警告: {e}")
        else:
            print(f"  ❌ {message}")
    
    print(f"\n📈 有效模型: {len(valid_models)}/{len(found_models)}")
    
    if valid_models:
        print("\n🎯 推荐使用的模型:")
        for model in valid_models:
            print(f"  - {model['name']} ({model['size']:.1f} MB) - {model['folder']}")
    
    return len(valid_models) > 0

def create_model_config():
    """创建模型配置文件"""
    config_content = """# HohoAI 模型配置文件
# 程序会自动读取此配置

# ==================== 模型设置 ====================
# 默认YOLO模型文件名（程序会自动搜索）
default_yolo_model=yolov8n.pt

# 模型搜索路径（按优先级排序）
model_search_paths=models/yolo,models/weights,models,.

# 模型置信度阈值
model_confidence=0.6

# 是否启用GPU加速
use_gpu=true

# ==================== 错误处理 ====================
# 是否显示详细错误信息
verbose_errors=true

# 是否在控制台打印模型加载信息
print_model_info=true

# 模型加载失败时的备用方案
fallback_to_cpu=true

# ==================== 性能设置 ====================
# 模型推理设备 (auto/cpu/cuda/mps)
inference_device=auto

# 批处理大小
batch_size=1

# 图像输入尺寸
input_size=640
"""
    
    try:
        with open("models/model_config.txt", "w", encoding="utf-8") as f:
            f.write(config_content)
        print("✅ 创建模型配置文件: models/model_config.txt")
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")

def main():
    """主函数"""
    print("🎯 HohoAI 模型管理器")
    print("=" * 50)
    
    try:
        # 创建文件夹结构
        create_model_structure()
        
        # 创建说明文件
        create_model_readme()
        
        # 创建配置文件
        create_model_config()
        
        # 扫描和测试模型
        has_valid_models = test_model_loading()
        
        print("\n" + "=" * 50)
        
        if has_valid_models:
            print("✅ 模型管理器设置完成")
            print("🎯 找到有效模型，可以开始使用")
        else:
            print("⚠️ 模型管理器设置完成，但未找到有效模型")
            print("📥 请将模型文件放入 models/yolo/ 文件夹")
            print("💡 推荐下载 yolov8n.pt 模型")
        
        print("\n📁 文件夹结构已创建:")
        print("  - models/yolo/     (放置YOLO模型)")
        print("  - models/weights/  (放置权重文件)")
        print("  - models/config/   (放置配置文件)")
        print("  - models/backup/   (备份文件)")
        
        print("\n📖 查看详细说明: models/README.md")
        print("⚙️ 修改配置: models/model_config.txt")
        
    except Exception as e:
        print(f"\n❌ 模型管理器运行失败: {e}")
        import traceback
        print("\n🔍 详细错误信息:")
        traceback.print_exc()

if __name__ == "__main__":
    main()
