# HohoAI - 游戏辅助工具

## 🎯 快速开始

### 主要文件
- **`enhanced_hotreload_hohoai.py`** - 主启动器（推荐）
- **`hohoai.py`** - 原版程序
- **`model_manager.py`** - 模型管理器
- **`yolo_fix.py`** - YOLO修复工具

### 配置文件
- **`hohoai_params.txt`** - 参数配置
- **`stealth_config.txt`** - 隐蔽配置
- **`简易配置.txt`** - 简易配置说明

### 使用方法

#### 推荐方式（反检测模式）
```bash
python enhanced_hotreload_hohoai.py
```

#### 普通方式
```bash
python hohoai.py
```

## 📁 文件夹结构

```
HohoAI/
├── enhanced_hotreload_hohoai.py  # 主启动器
├── hohoai.py                     # 原版程序
├── model_manager.py              # 模型管理器
├── yolo_fix.py                   # YOLO修复工具
├── hohoai_params.txt             # 参数配置
├── stealth_config.txt            # 隐蔽配置
├── models/                       # 模型文件夹
│   ├── yolo/                    # YOLO模型
│   ├── weights/                 # 权重文件
│   └── config/                  # 配置文件
├── Module/                       # 核心模块
├── Utils/                        # 工具模块
├── DLLs/                         # 动态链接库
├── MY_apex/                      # Apex模型
├── Tools/                        # 工具集
├── UI/                           # 界面文件
└── logs/                         # 日志文件
```

## 🚀 功能特点

### 反检测功能
- ✅ 进程伪装（Audio Service Host）
- ✅ 完全隐形运行（无GUI）
- ✅ 参数热重载（无需重启）
- ✅ 详细错误显示
- ✅ 自动模型管理

### 核心功能
- 🎯 自动瞄准
- 🔫 自动开火
- 🎮 自动扳机
- 📊 实时检测
- ⚙️ 参数调节

## 📋 使用说明

1. **放置模型文件**
   - 将YOLO模型文件放入 `models/yolo/` 文件夹
   - 推荐使用 `yolov8n.pt` 或 `yolov8s.pt`

2. **运行程序**
   ```bash
   python enhanced_hotreload_hohoai.py
   ```

3. **调整参数**
   - 程序会自动打开 `hohoai_params.txt`
   - 修改参数后保存即可生效
   - 无需重启程序

4. **开始使用**
   - 程序在后台隐形运行
   - 所有功能正常工作
   - 无任何可见界面

## ⚙️ 参数说明

### 基本参数
```txt
yolo_confidence=0.6      # 检测置信度
aim_range=100           # 瞄准范围
aim_speed_x=2.0         # 水平瞄准速度
aim_speed_y=2.0         # 垂直瞄准速度
```

### 功能开关
```txt
auto_aim=true           # 自动瞄准
auto_fire=true          # 自动开火
auto_trigger=true       # 自动扳机
```

## 🔧 故障排除

### 常见问题
- **程序无法启动** → 检查模型文件是否存在
- **检测不准确** → 调整 `yolo_confidence` 参数
- **瞄准偏移** → 调整 `offset_center_x/y` 参数

### 错误日志
- 控制台显示实时错误
- 详细错误保存在 `error.log`
- 运行配置保存在 `runtime_config.json`

## 📞 支持信息

详细使用说明请查看：`反检测使用说明.md`

---

**© 2024 HohoAI. 仅供学习研究使用。**
