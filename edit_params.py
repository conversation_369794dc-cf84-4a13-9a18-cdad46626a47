#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HohoAI 参数编辑器
方便修改配置参数
"""

import os
import sys
import subprocess
import time

def open_params_file():
    """打开参数文件"""
    try:
        if os.name == 'nt':
            subprocess.Popen(['notepad.exe', 'hohoai_params.txt'])
            return True
    except Exception as e:
        print(f"❌ 无法打开参数文件: {e}")
        return False

def show_current_params():
    """显示当前参数"""
    if not os.path.exists("hohoai_params.txt"):
        print("❌ 参数文件不存在")
        return
    
    print("📋 当前参数设置:")
    print("=" * 50)
    
    try:
        with open("hohoai_params.txt", "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        # 只显示非注释行
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                print(f"  {key.strip():<20} = {value.strip()}")
                
    except Exception as e:
        print(f"❌ 读取参数文件失败: {e}")

def create_quick_configs():
    """创建快速配置模板"""
    
    # 无GUI配置
    no_gui_config = """# HohoAI 无GUI配置
yolo_confidence=0.6
aim_range=100
aim_speed_x=2.0
aim_speed_y=2.0
auto_aim=true
auto_fire=true
auto_trigger=true
show_video=false
show_gui=false
headless_mode=true
console_output=true
lock_speed_x=3.0
lock_speed_y=3.0
jump_suppression=50
offset_center_x=0.0
offset_center_y=0.5
"""
    
    # 有GUI配置
    with_gui_config = """# HohoAI 有GUI配置
yolo_confidence=0.6
aim_range=100
aim_speed_x=2.0
aim_speed_y=2.0
auto_aim=true
auto_fire=true
auto_trigger=true
show_video=true
show_gui=true
headless_mode=false
console_output=true
show_yolo_boxes=true
lock_speed_x=3.0
lock_speed_y=3.0
jump_suppression=50
offset_center_x=0.0
offset_center_y=0.5
"""
    
    # 高精度配置
    high_precision_config = """# HohoAI 高精度配置
yolo_confidence=0.8
aim_range=80
aim_speed_x=1.5
aim_speed_y=1.5
auto_aim=true
auto_fire=true
auto_trigger=true
show_video=false
show_gui=false
headless_mode=true
console_output=true
lock_speed_x=2.0
lock_speed_y=2.0
jump_suppression=30
offset_center_x=0.0
offset_center_y=0.3
"""
    
    # 高速度配置
    high_speed_config = """# HohoAI 高速度配置
yolo_confidence=0.5
aim_range=120
aim_speed_x=4.0
aim_speed_y=4.0
auto_aim=true
auto_fire=true
auto_trigger=true
show_video=false
show_gui=false
headless_mode=true
console_output=true
lock_speed_x=5.0
lock_speed_y=5.0
jump_suppression=70
offset_center_x=0.0
offset_center_y=0.7
"""
    
    configs = {
        "no_gui": no_gui_config,
        "with_gui": with_gui_config,
        "high_precision": high_precision_config,
        "high_speed": high_speed_config
    }
    
    for name, config in configs.items():
        filename = f"config_{name}.txt"
        with open(filename, "w", encoding="utf-8") as f:
            f.write(config)
    
    print("✅ 快速配置模板已创建:")
    print("  - config_no_gui.txt (无GUI配置)")
    print("  - config_with_gui.txt (有GUI配置)")
    print("  - config_high_precision.txt (高精度配置)")
    print("  - config_high_speed.txt (高速度配置)")

def apply_config(config_name):
    """应用配置"""
    config_file = f"config_{config_name}.txt"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件 {config_file} 不存在")
        return False
    
    try:
        # 备份当前配置
        if os.path.exists("hohoai_params.txt"):
            backup_name = f"hohoai_params_backup_{int(time.time())}.txt"
            with open("hohoai_params.txt", "r", encoding="utf-8") as src:
                with open(backup_name, "w", encoding="utf-8") as dst:
                    dst.write(src.read())
            print(f"✅ 当前配置已备份为: {backup_name}")
        
        # 应用新配置
        with open(config_file, "r", encoding="utf-8") as src:
            with open("hohoai_params.txt", "w", encoding="utf-8") as dst:
                dst.write(src.read())
        
        print(f"✅ 已应用配置: {config_name}")
        return True
        
    except Exception as e:
        print(f"❌ 应用配置失败: {e}")
        return False

def main():
    """主函数"""
    print("⚙️ HohoAI 参数编辑器")
    print("=" * 40)
    
    while True:
        print("\n📋 选择操作:")
        print("1. 查看当前参数")
        print("2. 编辑参数文件")
        print("3. 创建快速配置模板")
        print("4. 应用无GUI配置")
        print("5. 应用有GUI配置")
        print("6. 应用高精度配置")
        print("7. 应用高速度配置")
        print("8. 退出")
        
        try:
            choice = input("\n请选择 (1-8): ").strip()
            
            if choice == '1':
                show_current_params()
                
            elif choice == '2':
                print("📝 正在打开参数文件...")
                if open_params_file():
                    print("✅ 参数文件已打开")
                    print("💡 修改后保存即可生效")
                
            elif choice == '3':
                create_quick_configs()
                
            elif choice == '4':
                if apply_config("no_gui"):
                    print("🚫 无GUI配置已应用")
                
            elif choice == '5':
                if apply_config("with_gui"):
                    print("🖥️ 有GUI配置已应用")
                
            elif choice == '6':
                if apply_config("high_precision"):
                    print("🎯 高精度配置已应用")
                
            elif choice == '7':
                if apply_config("high_speed"):
                    print("⚡ 高速度配置已应用")
                
            elif choice == '8':
                print("👋 退出参数编辑器")
                break
                
            else:
                print("❌ 无效选择")
                
        except KeyboardInterrupt:
            print("\n\n🛑 用户中断")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
    
    print("\n💡 提示:")
    print("  - 参数修改后立即生效")
    print("  - 可随时重新运行此编辑器")
    print("  - 配置文件位置: hohoai_params.txt")

if __name__ == "__main__":
    main()
