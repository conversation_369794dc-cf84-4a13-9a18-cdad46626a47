# HohoAI 隐蔽配置文件
# 修改此文件中的参数来自定义您的反检测设置
# 格式：参数名=参数值
# 注意：不要删除等号，不要添加多余的空格

# ==================== 进程伪装设置 ====================
# 进程窗口标题（在任务管理器中显示的名称）
process_title=Audio Service Host

# 服务显示名称
service_name=Audio Driver Manager

# 服务版本号
service_version=3.1

# 服务描述
service_description=Initializing audio subsystem

# ==================== 文件伪装设置 ====================
# 创建的假系统文件名（用逗号分隔多个文件）
fake_files=audio_service.log,sound_status.tmp,audio_config.dat

# 日志文件名
log_file=audio_service.log

# ==================== 时间延迟设置 ====================
# 启动延迟范围（秒）- 格式：最小值,最大值
startup_delay=1,3

# 检查项目之间的延迟范围（秒）
check_delay=0.2,0.5

# 后台监控间隔（秒）
monitor_interval=60

# ==================== 安全检查项目 ====================
# 显示的检查项目（用逗号分隔）
security_checks=Audio hardware detection,Sound driver verification,Audio buffer allocation,DirectSound compatibility,Audio device configuration

# ==================== 诱饵进程设置 ====================
# 是否启用诱饵进程（true/false）
enable_decoy=true

# 诱饵命令（用分号分隔多个命令）
decoy_commands=ping 127.0.0.1 -n 1;tasklist /fi "imagename eq svchost.exe"

# ==================== 高级设置 ====================
# 是否启用后台监控线程（true/false）
enable_monitor=true

# 是否创建假系统文件（true/false）
create_fake_files=true

# 是否显示详细日志（true/false）
verbose_logging=true

# 目标程序文件名
target_file=hohoai.py

# ==================== 隐蔽模式设置 ====================
# 是否启用无GUI模式（true/false）
headless_mode=true

# 是否隐藏所有窗口（true/false）
hide_all_windows=true

# 是否禁用GUI显示（true/false）
disable_gui=true

# ==================== 错误处理设置 ====================
# 加载失败时的错误消息
load_error_msg=Graphics driver not found

# 执行失败时的错误消息
exec_error_msg=Graphics driver error

# 成功加载时的消息
success_msg=Audio driver loaded successfully

# ==================== 自定义消息 ====================
# 启动消息
startup_msg=Audio subsystem ready

# 初始化消息
init_msg=Loading audio driver

# 运行消息
running_msg=Audio service running

# 停止消息
stop_msg=Audio service stopped by user

# 终止消息
terminate_msg=Audio Driver Manager terminated

# ==================== 高级伪装选项 ====================
# 是否隐藏控制台窗口（true/false）
hide_console=false

# 是否设置进程优先级（normal/high/low）
process_priority=normal

# 是否启用内存保护（true/false）
memory_protection=true

# ==================== 检测规避设置 ====================
# 是否启用反调试检测（true/false）
anti_debug=true

# 是否启用虚拟机检测（true/false）
vm_detection=true

# 是否启用沙箱检测（true/false）
sandbox_detection=true

# ==================== 网络设置 ====================
# 是否启用网络活动伪装（true/false）
network_decoy=false

# 伪装网络请求地址
decoy_urls=http://www.microsoft.com,https://www.nvidia.com

# ==================== 使用说明 ====================
# 1. 修改上述参数后保存文件
# 2. 运行：python configurable_stealth_hohoai.py
# 3. 程序会自动读取此配置文件
# 4. 如果配置文件不存在或格式错误，将使用默认值

# ==================== 参数说明 ====================
# process_title: 任务管理器中显示的进程名
# service_name: 服务的显示名称
# startup_delay: 启动时的随机延迟，增加真实性
# security_checks: 启动时显示的检查项目，可自定义
# enable_decoy: 是否启动诱饵进程来混淆检测
# verbose_logging: 是否显示详细的运行信息
# target_file: 要加载的目标程序文件名

# ==================== 高级用户选项 ====================
# 以下选项仅供高级用户使用，不建议随意修改

# 内存分配模式（standard/optimized/secure）
memory_mode=standard

# 代码混淆级别（low/medium/high）
obfuscation_level=medium

# 检测规避强度（basic/enhanced/maximum）
evasion_strength=enhanced

# ==================== 实验性功能 ====================
# 以下功能为实验性，可能不稳定

# 是否启用动态进程名（true/false）
dynamic_process_name=false

# 是否启用行为随机化（true/false）
behavior_randomization=true

# 是否启用深度伪装（true/false）
deep_camouflage=false
