@echo off
title HohoAI No GUI Mode
echo HohoAI No GUI Mode
echo ==================
echo 正在停止所有Python进程...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im pythonw.exe >nul 2>&1
timeout /t 2 /nobreak >nul
echo Starting background service...
echo No GUI windows will be shown
echo Detection info will be displayed here
echo ==================

set QT_QPA_PLATFORM=offscreen
set DISPLAY=
set HEADLESS=1
set NO_GUI=1
set QT_LOGGING_RULES=*.debug=false;qt.qpa.*=false

python simple_no_gui_hohoai.py

echo.
echo Service stopped, press any key to close...
pause > nul
