# HohoAI 使用说明

## 🎯 主要文件说明

### 📁 核心文件
- **`simple_no_gui_hohoai.py`** - 🚫 **无GUI版本** (推荐主程序)
- **`hohoai.py`** - 🎮 **原版程序** (有GUI界面，包含卡密验证)
- **`check.py`** - 🔑 **卡密验证服务器**
- **`settings_with_comments.json`** - 📝 **配置文件** (带中文注释)
- **`Data/settings.json`** - 📝 **程序配置文件** (自动同步)


### 📁 启动脚本
- **`start_no_gui.bat`** - 🖱️ **一键启动无GUI版本**

## 🚀 启动方式

### 方式1: 无GUI版本 (推荐)
```bash
python simple_no_gui_hohoai.py
```
**特点:**
- ✅ 完全无GUI界面
- ✅ 后台运行
- ✅ 控制台显示检测信息
- ✅ 自动打开配置文件
- ✅ 跳过卡密验证

### 方式2: 批处理启动
```bash
双击 start_no_gui.bat
```
**特点:**
- ✅ 一键启动无GUI版本
- ✅ 自动设置环境变量

### 方式3: 原版程序 (包含卡密验证)
```bash
python hohoai.py
```
**特点:**
- ✅ 完整GUI界面
- ✅ 包含卡密验证系统
- ✅ 所有功能可见
- ❌ 可能被反作弊检测

## ⚙️ 参数配置

### 编辑带注释的配置文件 (推荐)
```bash
notepad settings_with_comments.json
```
**说明:**
- 包含详细的中文注释和修改说明
- 每个参数都有 true/false 等修改提示
- 程序直接读取此文件，修改后立即生效
- 启动无GUI版本时会自动打开

## 🔑 卡密验证系统

### 卡密服务器管理
```bash
python check.py
```
**功能:**
- 🔑 启动卡密验证服务器
- 📊 管理界面: http://localhost:5000
- ➕ 新增/删除/启用/禁用卡密
- 🔍 查看本地和远程卡密

### 原版程序卡密验证
- 运行 `python hohoai.py` 时会自动要求输入卡密
- 验证通过后才能使用程序
- 无GUI版本跳过卡密验证

## 📊 重要参数说明 (Data/settings.json)

```json
{
  "confidence": 0.3,           // YOLO检测置信度 (0.0-1.0)
  "aim_range": 132,            // 瞄准范围 (像素)
  "aim_speed_x": 5.0,          // X轴瞄准速度 (0.0-10.0)
  "aim_speed_y": 5.0,          // Y轴瞄准速度 (0.0-10.0)
  "aimBot": true,              // 自动瞄准开关
  "offset_centerx": 0.0,       // X轴偏移 (-1.0到1.0)
  "offset_centery": 0.45,      // Y轴偏移 (0.0=头部, 0.5=胸部, 1.0=腿部)
  "lockKey": "VK_RBUTTON",     // 触发热键
  "triggerType": "按下",        // 触发方式
  "mouse_Side_Button_Witch": false,  // 侧键瞄准
  "KmBox_IP": "*************", // KmBox IP地址
  "KmBox_PORT": "18243",       // KmBox 端口
  "KmBox_MAC": "6FE3E466"      // KmBox MAC地址
}
```

## 🎮 使用建议

### 推荐配置
1. **日常使用**: 运行 `python simple_no_gui_hohoai.py` 或双击 `start_no_gui.bat`
2. **参数调整**: 直接编辑 `Data/settings.json` 文件
3. **卡密管理**: 运行 `python check.py`

### 参数调整建议 (在 Data/settings.json 中修改)
- **提高精度**: `"confidence": 0.8`
- **提高速度**: `"aim_speed_x": 8.0, "aim_speed_y": 8.0`
- **调整瞄准点**: `"offset_centery": 0.5` (0.0=头部, 0.5=胸部, 1.0=腿部)
- **调整瞄准范围**: `"aim_range": 100` (像素)

## 🔧 故障排除

### 常见问题
1. **程序无响应**: 检查YOLO模型是否存在
2. **检测不准确**: 在 `Data/settings.json` 中调整 `"confidence"` 参数
3. **瞄准偏移**: 在 `Data/settings.json` 中调整 `"offset_centerx"` 和 `"offset_centery"` 参数
4. **配置不生效**: 确保修改的是 `Data/settings.json` 而不是 `hohoai_params.txt`

### 重置配置
如果配置出错，删除 `Data/settings.json` 文件，程序会自动创建默认配置。

## 📝 文件清单

### 保留的文件
- `hohoai.py` - 原版主程序 (包含卡密验证)
- `simple_no_gui_hohoai.py` - 无GUI版本 (推荐主脚本)
- `check.py` - 卡密验证服务器
- `start_no_gui.bat` - 启动脚本
- `Data/settings.json` - 配置文件 (程序实际使用)
- `edit_params.py` - 参数编辑器 (旧版)
- `hohoai_params.txt` - 参数文件 (仅供参考)

### 已删除的文件
- 所有测试版本和重复脚本已清理

---

**推荐使用 `simple_no_gui_hohoai.py` 作为主要启动方式！** 🎯✨

## 💡 使用流程

1. **启动程序**: `python simple_no_gui_hohoai.py`
2. **自动打开**: 程序会自动打开 `settings_with_comments.json` 配置文件
3. **查看注释**: 每个参数都有中文说明和 true/false 修改提示
4. **修改参数**: 在记事本中修改需要的参数 (如 true 改为 false)
5. **保存生效**: 保存文件后立即生效，无需重启程序
6. **开始使用**: 程序在后台运行检测

## 🔫 辅助压枪设置示例

在 `settings_with_comments.json` 中找到以下行：
```json
"recoil_suppression_switch": true,     // 辅助压枪开关 (true=开启, false=关闭)
"recoil_strength": 1,                  // 压枪强度 (1-100，数值越大压枪越强)
```

**开启压枪**: 将 `false` 改为 `true`
**关闭压枪**: 将 `true` 改为 `false`
**调整强度**: 修改数字 (1-100)

## 🚨 常见问题解决

### 问题1: 修改配置后不生效

**原因:** 可能有多个Python进程在运行

**解决方案:**
1. **停止所有Python进程:**
   ```bash
   双击 stop_all_python.bat
   ```

2. **同步配置文件:**
   ```bash
   python sync_all_configs.py
   ```

3. **重新启动程序:**
   ```bash
   python simple_no_gui_hohoai.py
   ```

### 问题2: GUI和无GUI版本配置不一致

**解决方案:**
1. **只运行一个版本** - 不要同时运行GUI和无GUI版本
2. **使用配置同步工具** - 运行 `sync_all_configs.py`
3. **检查配置状态** - 启动时会显示当前配置

### 问题3: 后台有多个Python进程

**检查进程:**
```bash
# 在命令行中运行
tasklist | findstr python.exe
```

**停止所有进程:**
```bash
# 使用批处理文件
stop_all_python.bat

# 或手动停止
taskkill /f /im python.exe
taskkill /f /im pythonw.exe
```

## 🔧 工具文件说明

### 新增工具文件:
- **`stop_all_python.bat`** - 停止所有Python进程
- **`sync_all_configs.py`** - 同步所有配置文件

### 使用流程:
1. **遇到问题时:** 运行 `stop_all_python.bat`
2. **同步配置:** 运行 `python sync_all_configs.py`
3. **重新启动:** 运行 `python simple_no_gui_hohoai.py`

## 💡 最佳实践

1. **单一程序运行** - 同时只运行一个版本
2. **配置前停止** - 修改配置前先停止所有进程
3. **检查状态** - 启动时查看配置状态显示
4. **使用工具** - 利用提供的批处理和Python工具

**现在您的配置修改问题应该完全解决了！** 🎯📝✨

## 🔥 新功能: 热重载 + 内存执行

### 🔄 热重载配置系统

**功能:** 修改配置文件保存后立即生效，无需重启程序

#### 使用方法:
```bash
# 方法1: 集成版本 (推荐)
python stealth_hohoai_with_hotreload.py

# 方法2: 单独启动热重载
python hot_reload_config.py
```

#### 特点:
- ✅ **实时监控** - 自动检测配置文件变化
- ✅ **立即生效** - 保存后1秒内生效
- ✅ **状态显示** - 显示配置更新信息
- ✅ **错误处理** - 配置错误时自动回滚

### 🧠 内存执行技术

**功能:** 将Python代码加密存储并在内存中执行，提高隐蔽性

#### 创建内存版本:
```bash
python memory_executor.py
# 选择选项1，创建内存启动器
```

#### 特点:
- ✅ **代码加密** - XOR + zlib + Base64多重加密
- ✅ **内存执行** - 代码在内存中运行，不留痕迹
- ✅ **进程伪装** - 伪装成"Windows Graphics Driver Service"
- ✅ **系统文件** - 创建假的系统日志文件

### 🔒 隐蔽版本 (推荐)

**最强版本:** 集成热重载 + 内存执行 + 进程伪装

```bash
python stealth_hohoai_with_hotreload.py
```

#### 功能特点:
- 🔄 **热重载配置** - 修改保存立即生效
- 🧠 **内存执行** - 高度隐蔽
- 🔒 **进程伪装** - 伪装成系统服务
- 📝 **自动打开配置** - 启动时自动打开配置文件
- 📊 **状态监控** - 实时显示配置状态

## 🎯 完整使用流程

### 日常使用 (推荐):
1. **启动隐蔽版本:**
   ```bash
   python stealth_hohoai_with_hotreload.py
   ```

2. **修改配置:**
   - 在自动打开的记事本中修改参数
   - 例如: 将 `"recoil_suppression_switch": true` 改为 `false`

3. **保存生效:**
   - 按 Ctrl+S 保存
   - 1秒内自动生效，控制台显示更新信息

4. **实时调整:**
   - 可随时修改配置
   - 无需重启程序
   - 立即看到效果

### 高级用户:
1. **创建内存版本:**
   ```bash
   python memory_executor.py
   # 选择选项1，输入源文件和输出文件
   ```

2. **运行内存版本:**
   ```bash
   python memory_hohoai.py
   ```

## 📊 功能对比

| 功能 | simple_no_gui | stealth_hohoai | 内存版本 |
|------|---------------|----------------|----------|
| 无GUI运行 | ✅ | ✅ | ✅ |
| 热重载配置 | ❌ | ✅ | ✅ |
| 进程伪装 | ❌ | ✅ | ✅ |
| 内存执行 | ❌ | ❌ | ✅ |
| 系统伪装 | ❌ | ✅ | ✅ |

**推荐使用: `stealth_hohoai_with_hotreload.py` - 功能最全面！** 🚀🔥✨
