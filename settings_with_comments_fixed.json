{
  "log_level": "info",                                    // 日志级别 (debug/info/warning/error)
  "aim_range": 132,                                       // 瞄准范围 (像素，建议80-200)
  "aimBot": true,                                         // 自动瞄准开关 (true=开启, false=关闭)
  "confidence": 0.0,                                      // YOLO检测置信度 (0.0-1.0，越高越严格)
  "aim_speed_x": 5.0,                                     // X轴瞄准速度 (0.0-10.0，越大越快)
  "aim_speed_y": 5.0,                                     // Y轴瞄准速度 (0.0-10.0，越大越快)
  "model_file": "C:/Users/<USER>/Desktop/fucklorant/best_fp16.engine",  // YOLO模型文件路径
  "mouse_Side_Button_Witch": false,                       // 侧键瞄准开关 (true=开启, false=关闭)
  "ProcessMode": "single_process",                        // 处理模式 (single_process/multi_process)
  "window_always_on_top": true,                           // 窗口置顶 (true=开启, false=关闭)
  "target_class": "0",                                    // 检测目标类别 (0=敌人, 1=队友, ALL=全部)
  "lockKey": "VK_RBUTTON",                               // 触发热键 (VK_LBUTTON=左键, VK_RBUTTON=右键)
  "triggerType": "按下",                                   // 触发方式 (按下/松开)
  "offset_centery": 0.45,                                // Y轴偏移 (0.0=头部, 0.5=胸部, 1.0=腿部)
  "offset_centerx": 0.0,                                 // X轴偏移 (-1.0到1.0，负数向左，正数向右)
  "screen_pixels_for_360_degrees": 6550,                 // 360度旋转所需像素数
  "screen_height_pixels": 3220,                          // 屏幕高度像素数
  "near_speed_multiplier": 2.5,                          // 近距离速度倍数
  "slow_zone_radius": 0,                                 // 慢速区域半径
  "mouseMoveMode": "kmbox",                              // 鼠标移动模式 (win32/kmbox)
  "lockSpeed": 5.5,                                      // 锁定速度
  "jump_suppression_switch": false,                      // 跳跃抑制开关 (true=开启, false=关闭)
  "jump_suppression_fluctuation_range": 18,              // 跳跃抑制误差范围 (像素)
  "wasd_detection_enabled": true,                        // WASD检测开关 (true=开启, false=关闭)
  "jump_detection_switch": false,                        // 跳跃检测开关 (true=开启, false=关闭)
  "KmBox_IP": "*************",                          // KmBox IP地址
  "KmBox_PORT": "18243",                                // KmBox 端口
  "KmBox_MAC": "6FE3E466",                              // KmBox MAC地址
  "recoil_strength": 5,                                  // 压枪强度 (1-100，数值越大压枪越强)
  "recoil_suppression_switch": false,                    // 辅助压枪开关 (true=开启, false=关闭)
  "auto_robot_enabled": false,                           // 自动机器人开关 (true=开启, false=关闭)
  "auto_robot_hotkey_vk": 80,                           // 自动机器人热键VK码 (71=G键, 80=P键)
  "auto_robot_hotkey_hex": null,                        // 自动机器人热键十六进制码
  "dynamic_aim_range_enabled": true,                     // 动态瞄准范围开关 (true=开启, false=关闭)
  "target_aim_range_scale": 0.2,                        // 有目标时瞄准范围缩放比例 (0.0-1.0)
  "auto_start": true,                                    // 自动启动 (true=开启, false=关闭)
  "show_video": true,                                    // 显示视频 (true=开启, false=关闭)
  "enable_yolo": true,                                   // 启用YOLO检测 (true=开启, false=关闭)
  "auto_aim": true,                                      // 自动瞄准 (true=开启, false=关闭)
  "auto_fire": true,                                     // 自动开火 (true=开启, false=关闭)
  "keep_running": true,                                  // 保持运行 (true=开启, false=关闭)
  "startup_message": "HohoAI 常开模式已激活"                // 启动消息
}
