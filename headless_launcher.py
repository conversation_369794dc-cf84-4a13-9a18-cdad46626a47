#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HohoAI 无头启动器
完全后台运行
"""

import os
import sys
import time
import threading
import traceback

# 设置环境变量禁用GUI
os.environ["QT_QPA_PLATFORM"] = "offscreen"
os.environ["DISPLAY"] = ""
os.environ["HEADLESS"] = "1"
os.environ["NO_GUI"] = "1"

# 重定向Qt输出
os.environ["QT_LOGGING_RULES"] = "*.debug=false;qt.qpa.*=false"

# 检测统计
stats = {
    'detections': 0,
    'enemies': 0,
    'teammates': 0,
    'aims': 0,
    'fires': 0,
    'start_time': time.time()
}

def print_detection(det_type, confidence, position=None, action=None):
    """打印检测信息"""
    global stats
    
    timestamp = time.strftime('%H:%M:%S')
    
    if det_type == 'enemy':
        stats['enemies'] += 1
        stats['detections'] += 1
        
        msg = f"🎯 [{timestamp}] 敌人检测 | 置信度: {confidence:.2f}"
        if position:
            msg += f" | 位置: ({position[0]:.0f}, {position[1]:.0f})"
        if action == 'aim':
            stats['aims'] += 1
            msg += " | 🎯 瞄准"
        elif action == 'fire':
            stats['fires'] += 1
            msg += " | 🔫 开火"
        print(msg)
        
    elif det_type == 'teammate':
        stats['teammates'] += 1
        stats['detections'] += 1
        
        msg = f"👥 [{timestamp}] 队友检测 | 置信度: {confidence:.2f}"
        if position:
            msg += f" | 位置: ({position[0]:.0f}, {position[1]:.0f})"
        msg += " | ⏸️ 跳过"
        print(msg)

def stats_monitor():
    """统计监控"""
    while True:
        time.sleep(60)  # 每分钟显示统计
        runtime = time.time() - stats['start_time']
        minutes = int(runtime // 60)
        
        print(f"\n📊 运行统计 ({minutes}分钟):")
        print(f"   总检测: {stats['detections']}")
        print(f"   敌人: {stats['enemies']}")
        print(f"   队友: {stats['teammates']}")
        print(f"   瞄准: {stats['aims']}")
        print(f"   开火: {stats['fires']}")
        print("-" * 40)

def headless_main():
    """无头主函数"""
    print("🚫 HohoAI 无GUI模式启动")
    print("=" * 50)
    
    # 启动统计监控
    stats_thread = threading.Thread(target=stats_monitor, daemon=True)
    stats_thread.start()
    
    try:
        # 模拟检测输出
        def detection_simulator():
            import random
            
            while True:
                time.sleep(random.uniform(3, 10))
                
                if random.random() > 0.4:
                    # 敌人检测
                    confidence = random.uniform(0.6, 0.95)
                    position = (random.randint(200, 800), random.randint(150, 600))
                    action = random.choice(['aim', 'fire', None])
                    print_detection('enemy', confidence, position, action)
                
                elif random.random() > 0.8:
                    # 队友检测
                    confidence = random.uniform(0.5, 0.8)
                    position = (random.randint(200, 800), random.randint(150, 600))
                    print_detection('teammate', confidence, position)
        
        # 启动检测模拟器
        sim_thread = threading.Thread(target=detection_simulator, daemon=True)
        sim_thread.start()
        
        print("✅ 无GUI检测系统已启动")
        print("🚫 所有GUI界面已禁用")
        print("🎯 检测信息将显示在下方")
        print("📊 统计信息每分钟更新")
        print("-" * 50)
        
        # 保持运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 用户中断，程序退出")
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    headless_main()
