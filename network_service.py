#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Network Service Host
"""

import os
import sys
import time
import ctypes
import random
import threading
import subprocess

# 伪装进程标题
try:
    ctypes.windll.kernel32.SetConsoleTitleW("Network Service Host")
except:
    pass

class NetworkServiceHost:
    """网络服务主机"""
    
    def __init__(self):
        self.service_active = False
        
    def create_service_files(self):
        """创建服务文件"""
        files = [
            "network_service.log",
            "service_status.tmp", 
            "network_config.dat"
        ]
        
        for file in files:
            try:
                if not os.path.exists(file):
                    with open(file, 'w') as f:
                        f.write(f"# Network Service Log\n")
                        f.write(f"# Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"Status: Running\n")
            except:
                pass
    
    def start_background_monitor(self):
        """启动后台监控"""
        def monitor():
            while self.service_active:
                try:
                    time.sleep(random.uniform(30, 60))
                    if random.random() < 0.1:
                        print("🔍 Network status check completed")
                except:
                    pass
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def start_main_service(self):
        """启动主服务"""
        try:
            print("🚀 Starting main service...")
            
            # 设置环境变量
            os.environ["QT_QPA_PLATFORM"] = "offscreen"
            os.environ["HEADLESS"] = "1"
            os.environ["NO_GUI"] = "1"
            
            # 启动主程序
            process = subprocess.Popen([
                sys.executable, "hohoai.py"
            ],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            stdin=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='ignore'
            )
            
            print("✅ Main service started")
            
            # 监控输出
            card_sent = False
            for line in iter(process.stdout.readline, ''):
                print(line.rstrip())
                
                if "请输入卡密：" in line and not card_sent:
                    try:
                        process.stdin.write("why\n")
                        process.stdin.flush()
                        card_sent = True
                        print("✅ Authentication completed")
                    except:
                        pass
            
            process.wait()
            
        except Exception as e:
            print(f"❌ Service error: {e}")
        finally:
            self.service_active = False
    
    def run(self):
        """运行服务"""
        print("🌐 Network Service Host")
        print("=" * 50)
        
        delay = random.uniform(1, 3)
        print(f"🔄 Initializing network service... ({delay:.1f}s)")
        time.sleep(delay)
        
        self.service_active = True
        
        # 创建服务文件
        self.create_service_files()
        print("📁 Service files created")
        
        # 启动后台监控
        self.start_background_monitor()
        print("📊 Background monitor started")
        
        print("=" * 50)
        print("✅ Network service ready")
        print("🔒 Process disguised as system service")
        print("=" * 50)
        
        # 启动主服务
        self.start_main_service()

if __name__ == "__main__":
    try:
        service = NetworkServiceHost()
        service.run()
    except KeyboardInterrupt:
        print("\n🛑 Network service stopped")
    except Exception as e:
        print(f"❌ Service error: {e}")
