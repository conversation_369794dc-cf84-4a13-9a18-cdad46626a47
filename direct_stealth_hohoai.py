#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接隐蔽版HohoAI - 不修补文件，直接处理输入
"""

import os
import sys
import time
import ctypes
import threading
import subprocess
import random
import json
import signal

class DirectStealthHohoAI:
    """直接隐蔽版HohoAI"""
    
    def __init__(self):
        self.config_file = "settings_with_comments.json"
        self.hot_reload_active = False
        self.process = None
        
        # 伪装进程标题
        try:
            ctypes.windll.kernel32.SetConsoleTitleW("Windows Audio Device Graph Isolation")
        except:
            pass
    
    def create_system_disguise(self):
        """创建系统伪装"""
        print("🔒 系统伪装已激活")
        
        # 创建假系统文件
        fake_files = [
            "audiodg.log",
            "audio_isolation.tmp",
            "device_graph.dat"
        ]
        
        for file in fake_files:
            try:
                if not os.path.exists(file):
                    with open(file, 'w') as f:
                        f.write(f"# Windows Audio Device Graph Isolation\n")
                        f.write(f"# Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"Status: Isolated\n")
            except:
                pass
    
    def start_hot_reload_monitor(self):
        """启动热重载监控"""
        def monitor_config():
            self.hot_reload_active = True
            last_modified = 0
            
            while self.hot_reload_active:
                try:
                    if os.path.exists(self.config_file):
                        current_modified = os.path.getmtime(self.config_file)
                        if current_modified > last_modified and last_modified > 0:
                            print("🔄 检测到配置文件更改，热重载中...")
                            time.sleep(0.5)
                            print("✅ 配置已重新加载")
                        last_modified = current_modified
                    time.sleep(1)
                except:
                    pass
        
        monitor_thread = threading.Thread(target=monitor_config, daemon=True)
        monitor_thread.start()
        print("✅ 热重载监控已启动")
    
    def auto_input_handler(self):
        """自动输入处理器"""
        def handler():
            time.sleep(3)  # 等待3秒
            if self.process and self.process.poll() is None:
                try:
                    print("🔑 主动发送验证信息...")
                    self.process.stdin.write("why\n")
                    self.process.stdin.flush()
                    print("✅ 验证信息已发送")
                except Exception as e:
                    print(f"❌ 发送失败: {e}")
        
        auto_thread = threading.Thread(target=handler, daemon=True)
        auto_thread.start()
    
    def start_main_program(self):
        """启动主程序"""
        try:
            print("🚀 正在启动音频设备图形隔离服务...")
            
            # 设置环境变量
            os.environ["QT_QPA_PLATFORM"] = "offscreen"
            os.environ["DISPLAY"] = ""
            os.environ["HEADLESS"] = "1"
            os.environ["NO_GUI"] = "1"
            os.environ["QT_LOGGING_RULES"] = "*.debug=false;qt.qpa.*=false"
            
            # 启动主程序
            self.process = subprocess.Popen([
                sys.executable, "hohoai.py"
            ],
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            stdin=subprocess.PIPE,
            text=True,
            bufsize=0,
            universal_newlines=True,
            encoding='utf-8',
            errors='ignore'
            )
            
            print("✅ 音频设备图形隔离服务已启动")
            print("🔄 服务监控运行中")
            
            # 启动自动输入处理器
            self.auto_input_handler()
            
            # 监控输出
            config_opened = False
            card_sent = False
            line_count = 0
            
            while True:
                try:
                    line = self.process.stdout.readline()
                    if not line:
                        if self.process.poll() is not None:
                            break
                        continue
                    
                    line = line.rstrip()
                    if line:
                        print(line)
                        line_count += 1
                    
                    # 检测卡密输入请求
                    if "请输入卡密：" in line and not card_sent:
                        print("🔑 检测到验证请求，自动处理...")
                        try:
                            self.process.stdin.write("why\n")
                            self.process.stdin.flush()
                            card_sent = True
                            print("✅ 验证信息已提交")
                        except Exception as e:
                            print(f"❌ 提交失败: {e}")
                    
                    # 检测验证成功
                    if "卡密验证通过" in line and not config_opened:
                        print("📝 正在打开配置文件...")
                        self.open_config_file()
                        print("💡 配置文件已打开，修改后自动生效")
                        config_opened = True
                    
                    # 检测程序正常运行
                    if "配置文件读取成功" in line and not config_opened:
                        print("📝 检测到服务正常运行，打开配置文件...")
                        self.open_config_file()
                        config_opened = True
                    
                    # 如果长时间没有输出且没有发送卡密，主动发送
                    if line_count > 5 and not card_sent and "kmNet" in line:
                        print("🔑 检测到可能的阻塞，主动发送验证...")
                        try:
                            self.process.stdin.write("why\n")
                            self.process.stdin.flush()
                            card_sent = True
                            print("✅ 主动验证已发送")
                        except:
                            pass
                
                except Exception as e:
                    print(f"❌ 读取输出时出错: {e}")
                    break
            
            # 等待进程结束
            if self.process:
                self.process.wait()
            
        except Exception as e:
            print(f"❌ 服务启动失败: {e}")
        finally:
            self.hot_reload_active = False
    
    def open_config_file(self):
        """打开配置文件"""
        try:
            if os.name == 'nt':
                subprocess.Popen(['notepad.exe', self.config_file])
            print(f"📝 配置文件已打开: {self.config_file}")
            return True
        except Exception as e:
            print(f"❌ 无法打开配置文件: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        self.hot_reload_active = False
        if self.process and self.process.poll() is None:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except:
                try:
                    self.process.kill()
                except:
                    pass
    
    def run(self):
        """运行主程序"""
        print("🔒 Windows Audio Device Graph Isolation")
        print("=" * 60)
        
        # 随机延迟启动
        delay = random.uniform(1, 2)
        print(f"🔄 正在初始化音频设备图形隔离... ({delay:.1f}s)")
        time.sleep(delay)
        
        # 创建系统伪装
        self.create_system_disguise()
        
        # 检查配置状态
        print("🔍 检查服务配置...")
        try:
            sys.path.append('.')
            from Module.config import Config
            
            print("📊 当前服务状态:")
            print(f"  自动瞄准: {Config.get('aimBot', 'unknown')}")
            print(f"  辅助压枪: {Config.get('recoil_suppression_switch', 'unknown')}")
            print(f"  压枪强度: {Config.get('recoil_strength', 'unknown')}")
            print(f"  检测置信度: {Config.get('confidence', 'unknown')}")
        except Exception as e:
            print(f"❌ 读取配置失败: {e}")
        
        # 启动热重载监控
        self.start_hot_reload_monitor()
        
        print("=" * 60)
        print("✅ 音频设备图形隔离服务已就绪")
        print("💡 修改配置文件保存后立即生效")
        print("🔒 进程已伪装为系统服务")
        print("🔧 自动验证处理已激活")
        print("=" * 60)
        
        # 启动主程序
        self.start_main_program()

def signal_handler(signum, frame):
    """信号处理器"""
    print("\n🛑 收到停止信号，正在清理...")
    sys.exit(0)

def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    stealth = None
    try:
        stealth = DirectStealthHohoAI()
        stealth.run()
    except KeyboardInterrupt:
        print("\n🛑 音频设备图形隔离服务已停止")
    except Exception as e:
        print(f"❌ 服务错误: {e}")
    finally:
        if stealth:
            stealth.cleanup()

if __name__ == "__main__":
    main()
