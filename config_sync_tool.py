#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件同步工具
将txt配置同步到JSON配置，或反之
"""

import json
import os
import re

class ConfigSyncTool:
    """配置同步工具"""
    
    def __init__(self):
        self.txt_file = "hohoai_params.txt"
        self.json_file = "settings_with_comments.json"
    
    def read_txt_config(self):
        """读取txt配置文件"""
        config = {}
        try:
            with open(self.txt_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        # 转换数据类型
                        if value.lower() == 'true':
                            config[key] = True
                        elif value.lower() == 'false':
                            config[key] = False
                        elif value.replace('.', '').isdigit():
                            config[key] = float(value) if '.' in value else int(value)
                        else:
                            config[key] = value
            
            print(f"✅ 成功读取txt配置: {len(config)} 项")
            return config
        except Exception as e:
            print(f"❌ 读取txt配置失败: {e}")
            return {}
    
    def read_json_config(self):
        """读取JSON配置文件"""
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 成功读取JSON配置: {len(config)} 项")
            return config
        except Exception as e:
            print(f"❌ 读取JSON配置失败: {e}")
            return {}
    
    def save_json_config(self, config):
        """保存JSON配置文件"""
        try:
            with open(self.json_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            print(f"✅ 成功保存JSON配置")
            return True
        except Exception as e:
            print(f"❌ 保存JSON配置失败: {e}")
            return False
    
    def map_txt_to_json(self, txt_config):
        """将txt配置映射到JSON格式"""
        mapping = {
            'yolo_confidence': 'confidence',
            'aim_range': 'aim_range',
            'aim_speed_x': 'aim_speed_x',
            'aim_speed_y': 'aim_speed_y',
            'auto_aim': 'aimBot',
            'lock_speed_x': 'lockSpeed',
            'lock_speed_y': 'lockSpeed',
            'offset_center_x': 'offset_centerx',
            'offset_center_y': 'offset_centery',
            'recoil_strength': 'recoil_strength',
            'recoil_enabled': 'recoil_suppression_switch',
        }
        
        json_config = {}
        for txt_key, value in txt_config.items():
            json_key = mapping.get(txt_key, txt_key)
            json_config[json_key] = value
        
        return json_config
    
    def sync_txt_to_json(self):
        """将txt配置同步到JSON"""
        print("🔄 正在将txt配置同步到JSON...")
        
        txt_config = self.read_txt_config()
        if not txt_config:
            return False
        
        json_config = self.read_json_config()
        mapped_config = self.map_txt_to_json(txt_config)
        
        # 合并配置
        json_config.update(mapped_config)
        
        return self.save_json_config(json_config)
    
    def show_current_configs(self):
        """显示当前配置"""
        print("📊 当前配置对比")
        print("=" * 60)
        
        txt_config = self.read_txt_config()
        json_config = self.read_json_config()
        
        print("\n🔤 TXT配置 (hohoai_params.txt):")
        for key, value in sorted(txt_config.items()):
            print(f"  {key}: {value}")
        
        print("\n📄 JSON配置 (settings_with_comments.json):")
        important_keys = [
            'aimBot', 'confidence', 'aim_range', 'aim_speed_x', 'aim_speed_y',
            'mouseMoveMode', 'recoil_suppression_switch', 'recoil_strength',
            'KmBox_IP', 'KmBox_PORT', 'KmBox_MAC'
        ]
        
        for key in important_keys:
            if key in json_config:
                print(f"  {key}: {json_config[key]}")
    
    def quick_fix_kmbox(self):
        """快速修复KmBox配置"""
        print("🔧 快速修复KmBox配置...")
        
        json_config = self.read_json_config()
        if not json_config:
            return False
        
        # 设置KmBox配置
        json_config.update({
            'mouseMoveMode': 'kmbox',
            'KmBox_IP': '*************',
            'KmBox_PORT': '18243',
            'KmBox_MAC': '6FE3E466'
        })
        
        if self.save_json_config(json_config):
            print("✅ KmBox配置已修复")
            print("📝 请重启程序以应用更改")
            return True
        return False
    
    def quick_fix_recoil(self):
        """快速修复压枪配置"""
        print("🔧 快速修复压枪配置...")
        
        json_config = self.read_json_config()
        if not json_config:
            return False
        
        # 设置压枪配置
        json_config.update({
            'recoil_suppression_switch': True,
            'recoil_strength': 20,
            'mouseMoveMode': 'win32'  # 使用win32模式更稳定
        })
        
        if self.save_json_config(json_config):
            print("✅ 压枪配置已修复")
            print("📝 请重启程序以应用更改")
            return True
        return False
    
    def show_menu(self):
        """显示菜单"""
        print("\n🛠️ 配置同步工具")
        print("=" * 40)
        print("1. 显示当前配置")
        print("2. 同步txt到JSON")
        print("3. 快速修复KmBox配置")
        print("4. 快速修复压枪配置")
        print("5. 修复所有配置")
        print("0. 退出")
        print("=" * 40)
    
    def run(self):
        """运行工具"""
        while True:
            self.show_menu()
            try:
                choice = input("请选择操作 (0-5): ").strip()
                
                if choice == "0":
                    print("👋 再见！")
                    break
                elif choice == "1":
                    self.show_current_configs()
                elif choice == "2":
                    self.sync_txt_to_json()
                elif choice == "3":
                    self.quick_fix_kmbox()
                elif choice == "4":
                    self.quick_fix_recoil()
                elif choice == "5":
                    print("🔧 修复所有配置...")
                    self.sync_txt_to_json()
                    self.quick_fix_kmbox()
                    self.quick_fix_recoil()
                    print("✅ 所有配置修复完成！")
                else:
                    print("❌ 无效选择，请重试")
                
                input("\n按回车键继续...")
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 操作失败: {e}")

def main():
    """主函数"""
    tool = ConfigSyncTool()
    tool.run()

if __name__ == "__main__":
    main()
