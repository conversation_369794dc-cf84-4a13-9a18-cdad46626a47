@echo off
title 停止所有Python进程
echo ========================================
echo           停止所有Python进程
echo ========================================
echo.

echo 正在查找Python进程...
tasklist | findstr python.exe
tasklist | findstr pythonw.exe

echo.
echo 正在停止所有Python进程...
taskkill /f /im python.exe
taskkill /f /im pythonw.exe

echo.
echo 等待进程完全停止...
timeout /t 3 /nobreak

echo.
echo 检查是否还有Python进程...
tasklist | findstr python.exe
tasklist | findstr pythonw.exe

echo.
echo 所有Python进程已停止
echo.
pause
