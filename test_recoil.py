#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
压枪功能测试脚本
"""

import time
import win32api
import json

def test_recoil_detection():
    """测试压枪检测功能"""
    print("🧪 压枪功能测试")
    print("=" * 40)
    
    # 读取配置
    try:
        with open('settings_with_comments.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        recoil_switch = config.get('recoil_suppression_switch', False)
        recoil_strength = config.get('recoil_strength', 0)
        mouse_mode = config.get('mouseMoveMode', 'unknown')
        
        print(f"📊 当前配置:")
        print(f"  压枪开关: {recoil_switch}")
        print(f"  压枪强度: {recoil_strength}")
        print(f"  鼠标模式: {mouse_mode}")
        print()
        
        if not recoil_switch:
            print("❌ 压枪开关未开启")
            return False
        
        if recoil_strength <= 0:
            print("❌ 压枪强度为0或负数")
            return False
        
        print("✅ 配置检查通过")
        
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")
        return False
    
    # 测试鼠标左键检测
    print("\n🖱️ 鼠标左键检测测试")
    print("请按住鼠标左键5秒钟...")
    
    start_time = time.time()
    left_button_pressed_time = 0
    
    while time.time() - start_time < 5:
        left_button_down = bool(win32api.GetAsyncKeyState(0x01) & 0x8000)
        if left_button_down:
            left_button_pressed_time += 0.1
            print("🔴 检测到鼠标左键按下", end='\r')
        else:
            print("⚪ 鼠标左键未按下    ", end='\r')
        time.sleep(0.1)
    
    print(f"\n📊 测试结果:")
    print(f"  左键按下时间: {left_button_pressed_time:.1f}秒")
    
    if left_button_pressed_time > 1:
        print("✅ 鼠标左键检测正常")
        return True
    else:
        print("❌ 鼠标左键检测异常")
        return False

def simulate_recoil_logic():
    """模拟压枪逻辑"""
    print("\n🎯 模拟压枪逻辑测试")
    print("按住鼠标左键测试压枪计算...")
    
    try:
        with open('settings_with_comments.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        recoil_strength = config.get('recoil_strength', 5)
        recoil_accumulator = 0.0
        
        print(f"压枪强度: {recoil_strength}")
        print("开始测试（10秒）...")
        
        start_time = time.time()
        move_count = 0
        
        while time.time() - start_time < 10:
            left_button_down = bool(win32api.GetAsyncKeyState(0x01) & 0x8000)
            
            if left_button_down:
                # 模拟压枪计算
                recoil_accumulator += recoil_strength / 900.0
                if recoil_accumulator >= 1.0:
                    move_pixels = int(recoil_accumulator)
                    print(f"🎯 应该向下移动 {move_pixels} 像素")
                    recoil_accumulator -= move_pixels
                    move_count += move_pixels
            else:
                recoil_accumulator = 0.0
            
            time.sleep(0.01)  # 模拟游戏帧率
        
        print(f"\n📊 测试结果:")
        print(f"  总移动像素: {move_count}")
        print(f"  平均每秒移动: {move_count/10:.1f} 像素")
        
        if move_count > 0:
            print("✅ 压枪逻辑计算正常")
        else:
            print("❌ 压枪逻辑未触发")
        
    except Exception as e:
        print(f"❌ 模拟测试失败: {e}")

def main():
    """主函数"""
    print("🔧 HohoAI 压枪功能诊断工具")
    print("=" * 50)
    
    # 配置检查
    config_ok = test_recoil_detection()
    
    if config_ok:
        # 逻辑测试
        simulate_recoil_logic()
    
    print("\n" + "=" * 50)
    print("📋 诊断建议:")
    
    try:
        with open('settings_with_comments.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        recoil_switch = config.get('recoil_suppression_switch', False)
        recoil_strength = config.get('recoil_strength', 0)
        mouse_mode = config.get('mouseMoveMode', 'unknown')
        
        if not recoil_switch:
            print("❗ 建议: 将 recoil_suppression_switch 设置为 true")
        
        if recoil_strength < 10:
            print("❗ 建议: 将 recoil_strength 增加到 10-30 之间")
        
        if mouse_mode == "kmbox":
            print("❗ 建议: 如果KmBox连接有问题，尝试改为 win32 模式")
        
        print("💡 使用提示:")
        print("  1. 确保在游戏中按住鼠标左键（开火键）")
        print("  2. 压枪只在开火时生效")
        print("  3. 如果使用KmBox，确保设备连接正常")
        
    except:
        pass
    
    print("=" * 50)

if __name__ == "__main__":
    main()
