# HohoAI 无GUI配置
# 后台运行模式，自动打开参数文件

# ==================== 基本设置 ====================
# YOLO检测置信度 (0.0-1.0，越高越严格)
yolo_confidence=0.6

# 瞄准范围 (像素，检测框中心到屏幕中心的最大距离)
aim_range=100

# 瞄准速度X轴 (0.0-10.0，越大越快)
aim_speed_x=2.0

# 瞄准速度Y轴 (0.0-10.0，越大越快)
aim_speed_y=2.0

# ==================== 自动功能开关 ====================
# 是否启用自动瞄准 (true/false)
auto_aim=true

# 是否启用自动开火 (true/false)
auto_fire=true

# 是否启用自动扳机 (true/false)
auto_trigger=true

# ==================== 显示设置 (无GUI模式) ====================
# 是否显示视频窗口 (true/false) - 无GUI模式建议false
show_video=false

# 是否显示GUI界面 (true/false) - 无GUI模式必须false
show_gui=false

# 是否启用无头模式 (true/false) - 后台运行必须true
headless_mode=true

# 是否在控制台输出检测信息 (true/false)
console_output=true

# 是否显示YOLO检测框 (true/false) - 无GUI模式无效
show_yolo_boxes=false

# ==================== 瞄准设置 ====================
# 锁定速度X轴 (0.0-10.0，锁定目标时的移动速度)
lock_speed_x=3.0

# 锁定速度Y轴 (0.0-10.0，锁定目标时的移动速度)
lock_speed_y=3.0

# 跳跃抑制范围 (0-100，跳跃时减少瞄准幅度)
jump_suppression=50

# 瞄准偏移X轴 (-1.0到1.0，负数向左，正数向右)
offset_center_x=0.0

# 瞄准偏移Y轴 (-1.0到1.0，负数向上，正数向下)
offset_center_y=0.5

# ==================== 高级设置 ====================
# 反应延迟 (毫秒，检测到目标后的反应时间)
reaction_delay=50

# 开火延迟 (毫秒，瞄准后开火的延迟时间)
fire_delay=100

# 连发间隔 (毫秒，连续开火的间隔时间)
burst_interval=150

# 最大连发次数 (次，单次检测的最大开火次数)
max_burst_count=3

# ==================== 检测过滤 ====================
# 最小目标尺寸 (像素，小于此尺寸的目标将被忽略)
min_target_size=20

# 最大目标尺寸 (像素，大于此尺寸的目标将被忽略)
max_target_size=300

# 边缘忽略区域 (像素，屏幕边缘多少像素内的目标将被忽略)
edge_ignore=50

# ==================== 性能设置 ====================
# 检测帧率 (FPS，每秒检测次数，越高越耗性能)
detection_fps=60

# GPU加速 (true/false，是否使用GPU加速)
use_gpu=true

# 多线程处理 (true/false，是否使用多线程)
use_multithread=true

# ==================== 调试设置 ====================
# 调试模式 (true/false，是否显示详细调试信息)
debug_mode=false

# 保存检测日志 (true/false，是否保存检测记录到文件)
save_log=false

# 日志文件路径 (检测日志保存位置)
log_file=detection_log.txt
