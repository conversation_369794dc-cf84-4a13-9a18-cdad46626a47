#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无卡密隐蔽版HohoAI - 完全跳过卡密验证
"""

import os
import sys
import time
import ctypes
import threading
import subprocess
import random
import json

class NoCardStealthHohoAI:
    """无卡密隐蔽版HohoAI"""
    
    def __init__(self):
        self.config_file = "settings_with_comments.json"
        self.hot_reload_active = False
        self.process = None
        
        # 伪装进程标题
        try:
            ctypes.windll.kernel32.SetConsoleTitleW("Windows Security Health Service")
        except:
            pass
    
    def create_system_disguise(self):
        """创建系统伪装"""
        print("🔒 系统伪装已激活")
        
        # 创建假系统文件
        fake_files = [
            "security_health.log",
            "health_status.tmp",
            "security_config.dat"
        ]
        
        for file in fake_files:
            try:
                if not os.path.exists(file):
                    with open(file, 'w') as f:
                        f.write(f"# Windows Security Health Service\n")
                        f.write(f"# Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"Status: Protected\n")
            except:
                pass
    
    def start_hot_reload_monitor(self):
        """启动热重载监控"""
        def monitor_config():
            self.hot_reload_active = True
            last_modified = 0
            
            while self.hot_reload_active:
                try:
                    if os.path.exists(self.config_file):
                        current_modified = os.path.getmtime(self.config_file)
                        if current_modified > last_modified and last_modified > 0:
                            print("🔄 检测到配置文件更改，热重载中...")
                            time.sleep(0.5)
                            print("✅ 配置已重新加载")
                        last_modified = current_modified
                    time.sleep(1)
                except:
                    pass
        
        monitor_thread = threading.Thread(target=monitor_config, daemon=True)
        monitor_thread.start()
        print("✅ 热重载监控已启动")
    
    def create_patched_main(self):
        """创建跳过卡密验证的主程序"""
        try:
            # 读取原始主程序
            with open('hohoai.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 完全移除卡密验证调用
            patched_content = content.replace(
                'if __name__ == \'__main__\':\n    # 僅本地卡密驗證\n    card_key_check()',
                'if __name__ == \'__main__\':\n    # 卡密验证已跳过\n    print("🔓 安全验证已通过")'
            )
            
            # 如果上面没有匹配，尝试其他模式
            if patched_content == content:
                patched_content = content.replace(
                    'card_key_check()',
                    'print("🔓 安全验证已通过")  # 跳过卡密验证'
                )
            
            # 更通用的替换
            if patched_content == content:
                import re
                # 替换所有card_key_check调用
                patched_content = re.sub(
                    r'card_key_check\(\)',
                    'print("🔓 安全验证已通过")  # 跳过卡密验证',
                    content
                )
            
            # 保存修补版本
            with open('hohoai_no_card.py', 'w', encoding='utf-8') as f:
                f.write(patched_content)
            
            print("🔧 已创建无卡密版本: hohoai_no_card.py")
            return True
            
        except Exception as e:
            print(f"❌ 创建修补版本失败: {e}")
            return False
    
    def start_main_program(self):
        """启动主程序"""
        try:
            print("🚀 正在启动安全健康服务...")
            
            # 创建修补版本
            if not self.create_patched_main():
                print("❌ 无法创建修补版本，程序退出")
                return
            
            # 设置环境变量
            os.environ["QT_QPA_PLATFORM"] = "offscreen"
            os.environ["DISPLAY"] = ""
            os.environ["HEADLESS"] = "1"
            os.environ["NO_GUI"] = "1"
            os.environ["QT_LOGGING_RULES"] = "*.debug=false;qt.qpa.*=false"
            
            # 启动修补版本
            self.process = subprocess.Popen([
                sys.executable, "hohoai_no_card.py"
            ],
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=0,
            universal_newlines=True,
            encoding='utf-8',
            errors='ignore'
            )
            
            print("✅ 安全健康服务已启动")
            print("🔄 服务监控运行中")
            
            # 监控输出
            config_opened = False
            line_count = 0
            
            while True:
                try:
                    line = self.process.stdout.readline()
                    if not line:
                        if self.process.poll() is not None:
                            break
                        continue
                    
                    line = line.rstrip()
                    if line:
                        print(line)
                        line_count += 1
                    
                    # 检测安全验证通过
                    if "安全验证已通过" in line and not config_opened:
                        print("📝 正在打开配置文件...")
                        self.open_config_file()
                        print("💡 配置文件已打开，修改后自动生效")
                        config_opened = True
                    
                    # 检测程序正常运行
                    if "配置文件读取成功" in line and not config_opened:
                        print("📝 检测到服务正常运行，打开配置文件...")
                        self.open_config_file()
                        config_opened = True
                    
                    # 检测YOLO加载完成
                    if "YOLO 模型" in line and "已加载" in line and not config_opened:
                        print("📝 检测到模型加载完成，打开配置文件...")
                        self.open_config_file()
                        config_opened = True
                
                except Exception as e:
                    print(f"❌ 读取输出时出错: {e}")
                    break
            
            # 等待进程结束
            if self.process:
                self.process.wait()
            
        except Exception as e:
            print(f"❌ 服务启动失败: {e}")
        finally:
            self.hot_reload_active = False
            # 清理临时文件
            try:
                if os.path.exists('hohoai_no_card.py'):
                    os.remove('hohoai_no_card.py')
                    print("🧹 临时文件已清理")
            except:
                pass
    
    def open_config_file(self):
        """打开配置文件"""
        try:
            if os.name == 'nt':
                subprocess.Popen(['notepad.exe', self.config_file])
            print(f"📝 配置文件已打开: {self.config_file}")
            return True
        except Exception as e:
            print(f"❌ 无法打开配置文件: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        self.hot_reload_active = False
        if self.process and self.process.poll() is None:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except:
                try:
                    self.process.kill()
                except:
                    pass
        
        # 清理临时文件
        try:
            if os.path.exists('hohoai_no_card.py'):
                os.remove('hohoai_no_card.py')
        except:
            pass
    
    def run(self):
        """运行主程序"""
        print("🔒 Windows Security Health Service")
        print("=" * 60)
        
        # 随机延迟启动
        delay = random.uniform(1, 2)
        print(f"🔄 正在初始化安全健康服务... ({delay:.1f}s)")
        time.sleep(delay)
        
        # 创建系统伪装
        self.create_system_disguise()
        
        # 检查配置状态
        print("🔍 检查服务配置...")
        try:
            sys.path.append('.')
            from Module.config import Config
            
            print("📊 当前服务状态:")
            print(f"  自动瞄准: {Config.get('aimBot', 'unknown')}")
            print(f"  辅助压枪: {Config.get('recoil_suppression_switch', 'unknown')}")
            print(f"  压枪强度: {Config.get('recoil_strength', 'unknown')}")
            print(f"  检测置信度: {Config.get('confidence', 'unknown')}")
        except Exception as e:
            print(f"❌ 读取配置失败: {e}")
        
        # 启动热重载监控
        self.start_hot_reload_monitor()
        
        print("=" * 60)
        print("✅ 安全健康服务已就绪")
        print("💡 修改配置文件保存后立即生效")
        print("🔒 进程已伪装为系统服务")
        print("🔓 无需卡密验证，直接运行")
        print("=" * 60)
        
        # 启动主程序
        self.start_main_program()

def main():
    """主函数"""
    stealth = None
    try:
        stealth = NoCardStealthHohoAI()
        stealth.run()
    except KeyboardInterrupt:
        print("\n🛑 安全健康服务已停止")
    except Exception as e:
        print(f"❌ 服务错误: {e}")
    finally:
        if stealth:
            stealth.cleanup()

if __name__ == "__main__":
    main()
