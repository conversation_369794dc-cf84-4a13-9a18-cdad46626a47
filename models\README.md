# HohoAI 模型文件夹说明

## 📁 文件夹结构

### models/yolo/
放置 YOLO 模型文件：
- yolov8n.pt (推荐)
- yolov8s.pt
- yolov8m.pt
- yolov8l.pt
- yolov8x.pt
- 或其他自定义 YOLO 模型

### models/weights/
放置权重文件：
- 预训练权重
- 自定义训练权重
- 备份权重文件

### models/config/
放置配置文件：
- 模型配置文件
- 类别标签文件
- 训练配置文件

### models/backup/
备份文件夹：
- 旧版本模型
- 备份权重
- 历史配置

## 🎯 推荐模型

1. **yolov8n.pt** - 轻量级，速度快
2. **yolov8s.pt** - 平衡性能和速度
3. **yolov8m.pt** - 中等精度
4. **yolov8l.pt** - 高精度
5. **yolov8x.pt** - 最高精度

## 📥 下载地址

可以从以下地址下载模型：
- https://github.com/ultralytics/ultralytics
- https://github.com/ultralytics/assets/releases

## 🔧 使用说明

1. 将模型文件放入对应文件夹
2. 运行 model_manager.py 检测模型
3. 程序会自动识别并使用模型
