# HohoAI 简易配置文件
# 只包含最常用的设置，修改后保存即可

# ==================== 基本伪装设置 ====================
# 进程标题（任务管理器中显示的名称）
process_title=Graphics Service Host

# 服务名称
service_name=Graphics Service Manager

# 服务版本
service_version=2.8

# ==================== 时间设置 ====================
# 启动延迟（秒）- 格式：最小值,最大值
startup_delay=1,3

# 检查延迟（秒）
check_delay=0.2,0.5

# ==================== 功能开关 ====================
# 是否启用诱饵进程（true/false）
enable_decoy=true

# 是否启用后台监控（true/false）
enable_monitor=true

# 是否创建假文件（true/false）
create_fake_files=true

# 是否显示详细信息（true/false）
verbose_logging=true

# ==================== 目标程序 ====================
# 要运行的程序文件名
target_file=hohoai.py

# ==================== 自定义消息 ====================
# 成功启动时显示的消息
success_msg=Graphics driver loaded successfully

# 运行中显示的消息
running_msg=Graphics service running

# ==================== 使用说明 ====================
# 1. 修改上面的参数
# 2. 保存文件
# 3. 运行：python configurable_stealth_hohoai.py
# 4. 程序会自动读取这个配置

# ==================== 常用伪装选项 ====================
# 可以把 process_title 改成：
# - Windows Security Service
# - System Update Manager  
# - Network Configuration Tool
# - Device Driver Manager
# - Audio Service Host
# - Display Driver Service

# 可以把 service_name 改成：
# - Windows Security Manager
# - System Configuration Service
# - Network Driver Manager
# - Hardware Compatibility Service
