#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
隐蔽版HohoAI - 集成热重载和内存执行技术
"""

import os
import sys
import time
import base64
import zlib
import marshal
import ctypes
import threading
import subprocess
import random
import json

# 伪装进程标题
try:
    ctypes.windll.kernel32.SetConsoleTitleW("Windows Graphics Driver Service")
except:
    pass

class StealthHohoAI:
    """隐蔽版HohoAI"""
    
    def __init__(self):
        self.key = b"HohoAI_Stealth_2024"
        self.config_file = "settings_with_comments.json"
        self.hot_reload_active = False
        
    def xor_decrypt(self, data, key):
        """解密函数"""
        result = bytearray()
        key_len = len(key)
        for i, byte in enumerate(data):
            result.append(byte ^ key[i % key_len])
        return bytes(result)
    
    def create_system_disguise(self):
        """创建系统伪装"""
        try:
            # 创建临时目录
            temp_dir = os.path.join(os.environ.get('TEMP', '.'), 'GraphicsDriver')
            os.makedirs(temp_dir, exist_ok=True)
            
            # 创建假的系统文件
            system_files = [
                "driver_init.log",
                "graphics_service.tmp", 
                "display_config.dat",
                "gpu_monitor.cache"
            ]
            
            for file_name in system_files:
                file_path = os.path.join(temp_dir, file_name)
                with open(file_path, 'w') as f:
                    f.write(f"Graphics Driver Service - {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("Service initialized successfully\n")
                    f.write(f"Process ID: {os.getpid()}\n")
            
            print("🔒 系统伪装已激活")
            return True
            
        except Exception as e:
            print(f"⚠️ 系统伪装失败: {e}")
            return False
    
    def start_hot_reload_monitor(self):
        """启动热重载监控"""
        def monitor_config():
            """监控配置文件变化"""
            if not os.path.exists(self.config_file):
                print(f"❌ 配置文件不存在: {self.config_file}")
                return
            
            last_modified = os.path.getmtime(self.config_file)
            print(f"🔄 开始监控配置文件: {self.config_file}")
            
            while self.hot_reload_active:
                try:
                    if os.path.exists(self.config_file):
                        current_modified = os.path.getmtime(self.config_file)
                        if current_modified > last_modified:
                            print(f"📝 [{time.strftime('%H:%M:%S')}] 检测到配置文件变化")
                            self.reload_config()
                            last_modified = current_modified
                    time.sleep(1)
                except Exception as e:
                    print(f"❌ 监控出错: {e}")
                    time.sleep(5)
        
        self.hot_reload_active = True
        monitor_thread = threading.Thread(target=monitor_config, daemon=True)
        monitor_thread.start()
        
        print("✅ 热重载监控已启动")
        print("💡 现在修改配置文件保存后立即生效")
    
    def reload_config(self):
        """重新加载配置"""
        try:
            # 等待文件写入完成
            time.sleep(0.5)
            
            # 读取配置文件
            with open(self.config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 移除注释
            lines = content.split('\n')
            cleaned_lines = []
            for line in lines:
                comment_pos = line.find('//')
                if comment_pos != -1:
                    before_comment = line[:comment_pos]
                    quote_count = before_comment.count('"') - before_comment.count('\\"')
                    if quote_count % 2 == 0:
                        line = line[:comment_pos].rstrip()
                if line.strip():
                    cleaned_lines.append(line)
            
            clean_content = '\n'.join(cleaned_lines)
            config_data = json.loads(clean_content)
            
            # 更新配置到程序
            self.update_program_config(config_data)
            
            print("✅ 配置已热重载")
            
        except Exception as e:
            print(f"❌ 配置重载失败: {e}")
    
    def update_program_config(self, config_data):
        """更新程序配置"""
        try:
            # 更新Module/config.py中的配置
            sys.path.append('.')
            from Module.config import Config
            
            # 清除缓存并更新配置
            Config.content = config_data
            Config.save()
            
            # 显示重要配置变化
            print("📊 配置已更新:")
            print(f"  自动瞄准: {config_data.get('aimBot', 'unknown')}")
            print(f"  辅助压枪: {config_data.get('recoil_suppression_switch', 'unknown')}")
            print(f"  压枪强度: {config_data.get('recoil_strength', 'unknown')}")
            print(f"  检测置信度: {config_data.get('confidence', 'unknown')}")
                
        except Exception as e:
            print(f"❌ 更新程序配置失败: {e}")
    
    def start_main_program(self):
        """启动主程序"""
        try:
            print("🚀 正在启动图形驱动服务...")

            # 设置环境变量
            os.environ["QT_QPA_PLATFORM"] = "offscreen"
            os.environ["DISPLAY"] = ""
            os.environ["HEADLESS"] = "1"
            os.environ["NO_GUI"] = "1"
            os.environ["QT_LOGGING_RULES"] = "*.debug=false;qt.qpa.*=false"

            # 启动主程序
            process = subprocess.Popen([
                sys.executable, "hohoai.py"
            ],
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            stdin=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True,
            encoding='utf-8',
            errors='ignore'
            )

            print("✅ 图形驱动服务已启动")
            print("🔄 热重载监控运行中")

            # 监控输出，等待卡密验证
            config_opened = False
            card_input_sent = False

            for line in iter(process.stdout.readline, ''):
                print(line.rstrip())

                # 检测到卡密输入请求，自动输入卡密
                if "请输入卡密：" in line and not card_input_sent:
                    print("🔑 自动输入卡密...")
                    try:
                        process.stdin.write("why\n")
                        process.stdin.flush()
                        card_input_sent = True
                        print("✅ 卡密已自动输入")
                    except Exception as e:
                        print(f"❌ 自动输入卡密失败: {e}")

                # 检测到卡密验证通过后打开配置文件
                if "卡密验证通过" in line and not config_opened:
                    print("📝 正在打开配置文件...")
                    self.open_config_file()
                    print("💡 现在可以修改配置文件，保存后立即生效")
                    config_opened = True

            # 等待进程结束
            process.wait()

        except Exception as e:
            print(f"❌ 启动失败: {e}")
        finally:
            self.hot_reload_active = False
    
    def open_config_file(self):
        """打开配置文件"""
        try:
            if os.name == 'nt':
                subprocess.Popen(['notepad.exe', self.config_file])
            print(f"📝 配置文件已打开: {self.config_file}")
            return True
        except Exception as e:
            print(f"❌ 无法打开配置文件: {e}")
            return False
    
    def run(self):
        """运行主程序"""
        print("🔒 Windows Graphics Driver Service")
        print("=" * 50)
        
        # 随机延迟启动
        delay = random.uniform(1, 3)
        print(f"🔄 正在初始化服务... ({delay:.1f}s)")
        time.sleep(delay)
        
        # 创建系统伪装
        self.create_system_disguise()
        
        # 检查配置状态
        print("🔍 检查配置状态...")
        try:
            sys.path.append('.')
            from Module.config import Config
            
            print("📊 当前配置状态:")
            print(f"  自动瞄准: {Config.get('aimBot', 'unknown')}")
            print(f"  辅助压枪: {Config.get('recoil_suppression_switch', 'unknown')}")
            print(f"  压枪强度: {Config.get('recoil_strength', 'unknown')}")
            print(f"  检测置信度: {Config.get('confidence', 'unknown')}")
        except Exception as e:
            print(f"❌ 读取配置失败: {e}")
        
        # 启动热重载监控
        self.start_hot_reload_monitor()
        
        print("=" * 50)
        print("✅ 服务已就绪")
        print("💡 修改配置文件保存后立即生效")
        print("🔒 进程已伪装为系统服务")
        print("=" * 50)
        
        # 启动主程序
        self.start_main_program()

def main():
    """主函数"""
    try:
        stealth = StealthHohoAI()
        stealth.run()
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 服务错误: {e}")

if __name__ == "__main__":
    main()
