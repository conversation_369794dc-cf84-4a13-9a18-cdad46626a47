#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HohoAI 简单无GUI版本
自动打开参数文件，后台运行检测
"""

import os
import sys
import time
import subprocess
import ctypes
import threading
import json

def setup_console():
    """设置控制台"""
    if os.name == 'nt':
        ctypes.windll.kernel32.SetConsoleTitleW("HohoAI No GUI Service")

def create_no_gui_config():
    """创建无GUI配置"""
    config = """# HohoAI 无GUI配置
# 后台运行模式，自动打开参数文件

# ==================== 基本设置 ====================
# YOLO检测置信度 (0.0-1.0，越高越严格)
yolo_confidence=0.6

# 瞄准范围 (像素，检测框中心到屏幕中心的最大距离)
aim_range=100

# 瞄准速度X轴 (0.0-10.0，越大越快)
aim_speed_x=2.0

# 瞄准速度Y轴 (0.0-10.0，越大越快)
aim_speed_y=2.0

# ==================== 自动功能开关 ====================
# 是否启用自动瞄准 (true/false)
auto_aim=true

# 是否启用自动开火 (true/false)
auto_fire=true

# 是否启用自动扳机 (true/false)
auto_trigger=true

# ==================== 显示设置 (无GUI模式) ====================
# 是否显示视频窗口 (true/false) - 无GUI模式建议false
show_video=false

# 是否显示GUI界面 (true/false) - 无GUI模式必须false
show_gui=false

# 是否启用无头模式 (true/false) - 后台运行必须true
headless_mode=true

# 是否在控制台输出检测信息 (true/false)
console_output=true

# 是否显示YOLO检测框 (true/false) - 无GUI模式无效
show_yolo_boxes=false

# ==================== 瞄准设置 ====================
# 锁定速度X轴 (0.0-10.0，锁定目标时的移动速度)
lock_speed_x=3.0

# 锁定速度Y轴 (0.0-10.0，锁定目标时的移动速度)
lock_speed_y=3.0

# 跳跃抑制范围 (0-100，跳跃时减少瞄准幅度)
jump_suppression=50

# 瞄准偏移X轴 (-1.0到1.0，负数向左，正数向右)
offset_center_x=0.0

# 瞄准偏移Y轴 (-1.0到1.0，负数向上，正数向下)
offset_center_y=0.5

# ==================== 高级设置 ====================
# 反应延迟 (毫秒，检测到目标后的反应时间)
reaction_delay=50

# 开火延迟 (毫秒，瞄准后开火的延迟时间)
fire_delay=100

# 连发间隔 (毫秒，连续开火的间隔时间)
burst_interval=150

# 最大连发次数 (次，单次检测的最大开火次数)
max_burst_count=3

# ==================== 检测过滤 ====================
# 最小目标尺寸 (像素，小于此尺寸的目标将被忽略)
min_target_size=20

# 最大目标尺寸 (像素，大于此尺寸的目标将被忽略)
max_target_size=300

# 边缘忽略区域 (像素，屏幕边缘多少像素内的目标将被忽略)
edge_ignore=50

# ==================== 性能设置 ====================
# 检测帧率 (FPS，每秒检测次数，越高越耗性能)
detection_fps=60

# GPU加速 (true/false，是否使用GPU加速)
use_gpu=true

# 多线程处理 (true/false，是否使用多线程)
use_multithread=true

# ==================== 调试设置 ====================
# 调试模式 (true/false，是否显示详细调试信息)
debug_mode=false

# 保存检测日志 (true/false，是否保存检测记录到文件)
save_log=false

# 日志文件路径 (检测日志保存位置)
log_file=detection_log.txt
"""
    
    with open("hohoai_params.txt", "w", encoding="utf-8") as f:
        f.write(config)

def open_config_file():
    """打开配置文件"""
    try:
        if os.name == 'nt':
            # 打开带注释的配置文件
            subprocess.Popen(['notepad.exe', 'settings_with_comments.json'])
        return True
    except Exception as e:
        print(f"❌ 无法打开配置文件: {e}")
        return False

def start_hot_reload():
    """启动热重载监控"""
    try:
        # 启动热重载守护进程
        subprocess.Popen([
            sys.executable, "hot_reload_config.py"
        ],
        cwd=os.getcwd(),
        creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )
        return True
    except Exception as e:
        print(f"❌ 启动热重载失败: {e}")
        return False







def main():
    """主函数"""
    print("🚫 HohoAI 简单无GUI版本")
    print("=" * 40)
    
    # 设置控制台
    setup_console()
    
    # 创建无GUI配置
    create_no_gui_config()
    print("✅ 无GUI配置已创建")
    
    # 检查配置状态
    print("🔍 检查配置状态...")
    try:
        import sys
        sys.path.append('.')
        from Module.config import Config

        print("📊 当前配置状态:")
        print(f"  自动瞄准: {Config.get('aimBot', 'unknown')}")
        print(f"  辅助压枪: {Config.get('recoil_suppression_switch', 'unknown')}")
        print(f"  压枪强度: {Config.get('recoil_strength', 'unknown')}")
        print(f"  检测置信度: {Config.get('confidence', 'unknown')}")
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")

    # 启动热重载监控
    print("🔄 启动配置热重载监控...")
    if start_hot_reload():
        print("✅ 热重载监控已启动")
        print("💡 现在修改配置文件保存后立即生效")
    
    print("🚫 启动无GUI模式...")
    print("🎯 YOLO检测: 后台运行")
    print("🤖 自动功能: 全部启用")
    print("📊 检测信息: 控制台显示")
    print("🚫 GUI界面: 完全隐藏")
    print("📝 配置文件已打开 (settings_with_comments.json)")
    print("🔄 热重载监控已启动")
    print("💡 修改配置后保存即可立即生效，无需重启程序")
    print("🔫 辅助压枪功能已集成，可通过配置开关")
    print("📖 配置文件包含中文注释和修改说明")
    print("-" * 40)
    
    # 设置环境变量禁用GUI
    os.environ["QT_QPA_PLATFORM"] = "offscreen"
    os.environ["DISPLAY"] = ""
    os.environ["HEADLESS"] = "1"
    os.environ["NO_GUI"] = "1"
    os.environ["QT_LOGGING_RULES"] = "*.debug=false;qt.qpa.*=false"
    
    try:
        # 启动原版程序但隐藏GUI
        print("🚀 正在启动HohoAI后台服务...")
        
        process = subprocess.Popen([
            sys.executable, "hohoai.py"
        ],
        cwd=os.getcwd(),
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True,
        bufsize=1,
        universal_newlines=True
        )

        print("✅ HohoAI后台服务已启动")
        print("👁️ 观察下方的检测信息输出")
        print("-" * 40)

        # 监控输出，等待卡密验证
        config_opened = False
        for line in iter(process.stdout.readline, ''):
            print(line.rstrip())

            # 检测到卡密验证通过后打开配置文件
            if "卡密验证通过" in line and not config_opened:
                print("📝 正在打开配置文件...")
                open_config_file()
                print("💡 现在可以修改配置文件，保存后立即生效")
                config_opened = True

        # 等待进程结束
        process.wait()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断")
        if 'process' in locals():
            process.terminate()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
    
    print("\n👋 程序已退出")

if __name__ == "__main__":
    main()
