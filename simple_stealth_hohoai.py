#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化隐蔽版HohoAI - 直接跳过卡密验证
"""

import os
import sys
import time
import ctypes
import threading
import subprocess
import random
import json

class SimpleStealthHohoAI:
    """简化隐蔽版HohoAI"""
    
    def __init__(self):
        self.config_file = "settings_with_comments.json"
        self.hot_reload_active = False
        
        # 伪装进程标题
        try:
            ctypes.windll.kernel32.SetConsoleTitleW("Audio Service Host")
        except:
            pass
    
    def create_system_disguise(self):
        """创建系统伪装"""
        print("🔒 系统伪装已激活")
        
        # 创建假系统文件
        fake_files = [
            "audio_service.log",
            "sound_status.tmp",
            "audio_config.dat"
        ]
        
        for file in fake_files:
            try:
                if not os.path.exists(file):
                    with open(file, 'w') as f:
                        f.write(f"# Audio Service Log\n")
                        f.write(f"# Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"Status: Active\n")
            except:
                pass
    
    def start_hot_reload_monitor(self):
        """启动热重载监控"""
        def monitor_config():
            self.hot_reload_active = True
            last_modified = 0
            
            while self.hot_reload_active:
                try:
                    if os.path.exists(self.config_file):
                        current_modified = os.path.getmtime(self.config_file)
                        if current_modified > last_modified and last_modified > 0:
                            print("🔄 检测到配置文件更改，热重载中...")
                            time.sleep(0.5)
                            print("✅ 配置已重新加载")
                        last_modified = current_modified
                    time.sleep(1)
                except:
                    pass
        
        monitor_thread = threading.Thread(target=monitor_config, daemon=True)
        monitor_thread.start()
        print("✅ 热重载监控已启动")
    
    def patch_card_verification(self):
        """修补卡密验证"""
        try:
            # 读取主程序文件
            with open('hohoai.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 创建临时修补版本
            patched_content = content.replace(
                'card_key_check()',
                '# card_key_check()  # 已跳过卡密验证\nprint("🔑 卡密验证已跳过")'
            )
            
            # 保存临时文件
            with open('hohoai_patched.py', 'w', encoding='utf-8') as f:
                f.write(patched_content)
            
            print("🔧 卡密验证已修补")
            return True
            
        except Exception as e:
            print(f"❌ 修补失败: {e}")
            return False
    
    def start_main_program(self):
        """启动主程序"""
        try:
            print("🚀 正在启动音频服务...")
            
            # 尝试修补卡密验证
            use_patched = self.patch_card_verification()
            target_file = 'hohoai_patched.py' if use_patched else 'hohoai.py'
            
            # 设置环境变量
            os.environ["QT_QPA_PLATFORM"] = "offscreen"
            os.environ["DISPLAY"] = ""
            os.environ["HEADLESS"] = "1"
            os.environ["NO_GUI"] = "1"
            os.environ["QT_LOGGING_RULES"] = "*.debug=false;qt.qpa.*=false"
            
            # 启动主程序
            if use_patched:
                print("🔧 使用修补版本启动...")
            
            process = subprocess.Popen([
                sys.executable, target_file
            ],
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            stdin=subprocess.PIPE,
            text=True,
            bufsize=0,
            universal_newlines=True,
            encoding='utf-8',
            errors='ignore'
            )
            
            print("✅ 音频服务已启动")
            print("🔄 服务监控运行中")
            
            # 监控输出
            config_opened = False
            card_attempts = 0
            max_attempts = 3
            
            for line in iter(process.stdout.readline, ''):
                if line:
                    print(line.rstrip())
                
                # 如果还是遇到卡密验证，尝试自动输入
                if "请输入卡密：" in line and card_attempts < max_attempts:
                    print(f"🔑 尝试自动验证 ({card_attempts + 1}/{max_attempts})...")
                    try:
                        process.stdin.write("why\n")
                        process.stdin.flush()
                        card_attempts += 1
                        print("✅ 验证尝试完成")
                    except Exception as e:
                        print(f"❌ 验证失败: {e}")
                        card_attempts += 1
                
                # 检测到验证通过或跳过后打开配置文件
                if ("卡密验证通过" in line or "卡密验证已跳过" in line) and not config_opened:
                    print("📝 正在打开配置文件...")
                    self.open_config_file()
                    print("💡 配置文件已打开，修改后自动生效")
                    config_opened = True
                
                # 检测到程序正常运行的标志
                if "配置文件读取成功" in line and not config_opened:
                    print("📝 检测到程序正常运行，打开配置文件...")
                    self.open_config_file()
                    config_opened = True
            
            # 等待进程结束
            process.wait()
            
        except Exception as e:
            print(f"❌ 服务启动失败: {e}")
        finally:
            self.hot_reload_active = False
            # 清理临时文件
            try:
                if os.path.exists('hohoai_patched.py'):
                    os.remove('hohoai_patched.py')
            except:
                pass
    
    def open_config_file(self):
        """打开配置文件"""
        try:
            if os.name == 'nt':
                subprocess.Popen(['notepad.exe', self.config_file])
            print(f"📝 配置文件已打开: {self.config_file}")
            return True
        except Exception as e:
            print(f"❌ 无法打开配置文件: {e}")
            return False
    
    def run(self):
        """运行主程序"""
        print("🔒 Audio Service Host")
        print("=" * 50)
        
        # 随机延迟启动
        delay = random.uniform(1, 2)
        print(f"🔄 正在初始化音频服务... ({delay:.1f}s)")
        time.sleep(delay)
        
        # 创建系统伪装
        self.create_system_disguise()
        
        # 检查配置状态
        print("🔍 检查服务配置...")
        try:
            sys.path.append('.')
            from Module.config import Config
            
            print("📊 当前服务状态:")
            print(f"  自动瞄准: {Config.get('aimBot', 'unknown')}")
            print(f"  辅助压枪: {Config.get('recoil_suppression_switch', 'unknown')}")
            print(f"  压枪强度: {Config.get('recoil_strength', 'unknown')}")
            print(f"  检测置信度: {Config.get('confidence', 'unknown')}")
        except Exception as e:
            print(f"❌ 读取配置失败: {e}")
        
        # 启动热重载监控
        self.start_hot_reload_monitor()
        
        print("=" * 50)
        print("✅ 音频服务已就绪")
        print("💡 修改配置文件保存后立即生效")
        print("🔒 进程已伪装为系统服务")
        print("🔧 卡密验证已优化处理")
        print("=" * 50)
        
        # 启动主程序
        self.start_main_program()

def main():
    """主函数"""
    try:
        stealth = SimpleStealthHohoAI()
        stealth.run()
    except KeyboardInterrupt:
        print("\n🛑 音频服务已停止")
    except Exception as e:
        print(f"❌ 服务错误: {e}")

if __name__ == "__main__":
    main()
