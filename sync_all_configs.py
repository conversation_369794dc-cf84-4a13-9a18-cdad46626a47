#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置同步工具
确保所有配置文件保持一致
"""

import json
import os
import shutil
from pathlib import Path

def sync_configs():
    """同步所有配置文件"""
    print("🔄 开始同步配置文件...")
    
    # 配置文件路径
    main_config = "settings_with_comments.json"
    data_config = "Data/settings.json"
    
    if not os.path.exists(main_config):
        print(f"❌ 主配置文件不存在: {main_config}")
        return False
    
    try:
        # 读取主配置文件
        with open(main_config, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 移除注释
        lines = content.split('\n')
        cleaned_lines = []
        for line in lines:
            comment_pos = line.find('//')
            if comment_pos != -1:
                before_comment = line[:comment_pos]
                quote_count = before_comment.count('"') - before_comment.count('\\"')
                if quote_count % 2 == 0:
                    line = line[:comment_pos].rstrip()
            if line.strip():
                cleaned_lines.append(line)
        
        clean_content = '\n'.join(cleaned_lines)
        config_data = json.loads(clean_content)
        
        # 确保Data目录存在
        os.makedirs("Data", exist_ok=True)
        
        # 保存到Data/settings.json
        with open(data_config, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置已同步: {main_config} -> {data_config}")
        
        # 显示重要配置
        print("\n📊 当前配置状态:")
        print(f"  自动瞄准: {config_data.get('aimBot', 'unknown')}")
        print(f"  辅助压枪: {config_data.get('recoil_suppression_switch', 'unknown')}")
        print(f"  压枪强度: {config_data.get('recoil_strength', 'unknown')}")
        print(f"  检测置信度: {config_data.get('confidence', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 同步失败: {e}")
        return False

def check_running_processes():
    """检查正在运行的Python进程"""
    import subprocess
    
    print("\n🔍 检查正在运行的Python进程...")
    
    try:
        # 查找Python进程
        result = subprocess.run(['tasklist', '/fi', 'imagename eq python.exe'], 
                              capture_output=True, text=True, shell=True)
        
        if 'python.exe' in result.stdout:
            print("⚠️ 发现正在运行的Python进程:")
            print(result.stdout)
            
            choice = input("\n是否停止所有Python进程? (y/n): ").strip().lower()
            if choice == 'y':
                subprocess.run(['taskkill', '/f', '/im', 'python.exe'], shell=True)
                subprocess.run(['taskkill', '/f', '/im', 'pythonw.exe'], shell=True)
                print("✅ 已停止所有Python进程")
        else:
            print("✅ 没有发现正在运行的Python进程")
            
    except Exception as e:
        print(f"❌ 检查进程失败: {e}")

def main():
    """主函数"""
    print("⚙️ HohoAI 配置同步工具")
    print("=" * 50)
    
    # 检查进程
    check_running_processes()
    
    # 同步配置
    if sync_configs():
        print("\n✅ 配置同步完成")
        print("\n💡 建议:")
        print("  1. 确保只运行一个版本的程序")
        print("  2. 修改配置后重启程序")
        print("  3. 使用 stop_all_python.bat 停止所有Python进程")
    else:
        print("\n❌ 配置同步失败")
    
    print("\n" + "=" * 50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
